<?php
// إنشاء قاعدة البيانات والجداول
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء قاعدة البيانات - نظام إدارة مؤسسات القطاع</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Noto Sans Arabic', sans-serif; }</style>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h2><i class='fas fa-database'></i> إنشاء قاعدة البيانات</h2>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO('mysql:host=127.0.0.1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-info'>";
    echo "<h4>الخطوة 1: إنشاء قاعدة البيانات</h4>";
    
    // إنشاء قاعدة البيانات
    $sql = "CREATE DATABASE IF NOT EXISTS sector_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إنشاء قاعدة البيانات sector_management_system</p>";
    echo "</div>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=sector_management_system;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-info'>";
    echo "<h4>الخطوة 2: إنشاء الجداول</h4>";
    
    // إنشاء جدول المنظمات
    $sql = "CREATE TABLE IF NOT EXISTS organizations (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name_ar VARCHAR(255) NOT NULL COMMENT 'اسم المنظمة بالعربية',
        name_en VARCHAR(255) NULL COMMENT 'اسم المنظمة بالإنجليزية',
        registration_number VARCHAR(255) UNIQUE NOT NULL COMMENT 'رقم التسجيل',
        type ENUM('charity', 'ngo', 'foundation', 'association') NOT NULL COMMENT 'نوع المنظمة',
        status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending' COMMENT 'حالة المنظمة',
        description_ar TEXT NULL COMMENT 'وصف المنظمة بالعربية',
        email VARCHAR(255) NULL,
        phone VARCHAR(255) NULL,
        website VARCHAR(255) NULL,
        address TEXT NULL COMMENT 'العنوان',
        city VARCHAR(255) NULL COMMENT 'المدينة',
        region VARCHAR(255) NULL COMMENT 'المنطقة',
        annual_budget DECIMAL(15, 2) NULL COMMENT 'الميزانية السنوية',
        employees_count INT DEFAULT 0 COMMENT 'عدد الموظفين',
        volunteers_count INT DEFAULT 0 COMMENT 'عدد المتطوعين',
        performance_score DECIMAL(3, 2) NULL COMMENT 'درجة تقييم الأداء',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP NULL
    )";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إنشاء جدول المنظمات</p>";
    
    // إنشاء جدول المستخدمين
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(191) UNIQUE NOT NULL,
        email_verified_at TIMESTAMP NULL,
        password VARCHAR(255) NOT NULL,
        first_name_ar VARCHAR(191) NULL,
        last_name_ar VARCHAR(191) NULL,
        phone VARCHAR(191) NULL,
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        language VARCHAR(2) DEFAULT 'ar',
        last_login_at TIMESTAMP NULL,
        organization_id BIGINT UNSIGNED NULL,
        remember_token VARCHAR(100) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP NULL,
        INDEX idx_organization (organization_id)
    )";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إنشاء جدول المستخدمين</p>";
    
    // إنشاء جدول الأدوار
    $sql = "CREATE TABLE IF NOT EXISTS roles (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(191) UNIQUE NOT NULL COMMENT 'اسم الدور',
        display_name_ar VARCHAR(191) NOT NULL COMMENT 'اسم الدور بالعربية',
        description_ar TEXT NULL COMMENT 'وصف الدور بالعربية',
        is_system_role BOOLEAN DEFAULT FALSE COMMENT 'دور نظام',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إنشاء جدول الأدوار</p>";
    
    // إنشاء جدول الصلاحيات
    $sql = "CREATE TABLE IF NOT EXISTS permissions (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(191) UNIQUE NOT NULL COMMENT 'اسم الصلاحية',
        display_name_ar VARCHAR(191) NOT NULL COMMENT 'اسم الصلاحية بالعربية',
        module VARCHAR(191) NOT NULL COMMENT 'الوحدة',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إنشاء جدول الصلاحيات</p>";

    // إضافة Foreign Keys
    try {
        $sql = "ALTER TABLE users ADD CONSTRAINT fk_users_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE SET NULL";
        $pdo->exec($sql);
        echo "<p class='text-success'>✓ تم إضافة Foreign Key للمستخدمين</p>";
    } catch (PDOException $e) {
        // Foreign key قد يكون موجود مسبقاً
        echo "<p class='text-warning'>⚠ Foreign Key للمستخدمين موجود مسبقاً</p>";
    }

    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<h4>الخطوة 3: إدراج البيانات الأساسية</h4>";
    
    // إدراج منظمة تجريبية
    $sql = "INSERT IGNORE INTO organizations (name_ar, name_en, registration_number, type, status, description_ar, email, phone, website, address, city, region, annual_budget, employees_count, volunteers_count, performance_score) VALUES
    ('جمعية الخير الخيرية', 'Al-Khair Charity Association', 'REG-2024-001', 'charity', 'active', 'جمعية خيرية تهدف إلى خدمة المجتمع وتقديم المساعدات للمحتاجين', '<EMAIL>', '+966501234567', 'https://www.alkhair.org', 'شارع الملك فهد، الرياض', 'الرياض', 'منطقة الرياض', 5000000.00, 25, 100, 4.5)";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إدراج منظمة تجريبية</p>";
    
    // إدراج المستخدمين التجريبيين
    $sql = "INSERT IGNORE INTO users (name, email, password, first_name_ar, last_name_ar, phone, status, language, organization_id) VALUES
    ('مدير النظام', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', '+966501234567', 'active', 'ar', NULL),
    ('مدير المنظمة', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد', 'المحمد', '+966501234568', 'active', 'ar', 1),
    ('مدير الموارد البشرية', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة', 'العلي', '+966501234569', 'active', 'ar', 1),
    ('مدير المشاريع', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد', 'السعد', '+966501234570', 'active', 'ar', 1),
    ('مدير المالية', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'خالد', 'الأحمد', '+966501234571', 'active', 'ar', 1)";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إدراج المستخدمين التجريبيين</p>";
    
    // إدراج الأدوار
    $sql = "INSERT IGNORE INTO roles (name, display_name_ar, description_ar, is_system_role) VALUES
    ('super_admin', 'مدير النظام', 'مدير النظام الرئيسي مع جميع الصلاحيات', TRUE),
    ('organization_admin', 'مدير المنظمة', 'مدير المنظمة مع صلاحيات إدارة المنظمة', TRUE),
    ('hr_manager', 'مدير الموارد البشرية', 'مدير الموارد البشرية مع صلاحيات إدارة الموظفين والمتطوعين', TRUE),
    ('project_manager', 'مدير المشاريع', 'مدير المشاريع مع صلاحيات إدارة المشاريع والبرامج', TRUE),
    ('finance_manager', 'مدير المالية', 'مدير المالية مع صلاحيات إدارة الشؤون المالية والتبرعات', TRUE)";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إدراج الأدوار الأساسية</p>";
    
    echo "</div>";
    
    echo "<div class='alert alert-success'>";
    echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء قاعدة البيانات بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام النظام مع الحسابات التجريبية التالية:</p>";
    echo "<ul>";
    echo "<li><strong>مدير النظام:</strong> <EMAIL> / password</li>";
    echo "<li><strong>مدير المنظمة:</strong> <EMAIL> / password</li>";
    echo "<li><strong>مدير الموارد البشرية:</strong> <EMAIL> / password</li>";
    echo "<li><strong>مدير المشاريع:</strong> <EMAIL> / password</li>";
    echo "<li><strong>مدير المالية:</strong> <EMAIL> / password</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-exclamation-triangle'></i> خطأ في إنشاء قاعدة البيانات</h4>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>يرجى التأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<div class='text-center mt-3'>";
echo "<a href='test.php' class='btn btn-primary'>العودة لصفحة الاختبار</a>";
echo "<a href='index.php' class='btn btn-success'>الذهاب للنظام</a>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js'></script>";
echo "</body>";
echo "</html>";
?>
