# دليل التشغيل السريع - نظام إدارة مؤسسات القطاع

## 🚀 التشغيل السريع (5 دقائق)

### الخطوة 1: إعداد قاعدة البيانات
```bash
# تشغيل WAMP/XAMPP
# فتح phpMyAdmin أو MySQL Command Line

# إنشاء قاعدة البيانات
CREATE DATABASE sector_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### الخطوة 2: تشغيل الـ Migrations
```bash
# الانتقال لمجلد المشروع
cd C:\wamp64\www\CMS3\sector-management-system

# تشغيل الـ migrations
C:\wamp64\bin\php\php8.3.6\php.exe artisan migrate

# تشغيل الـ seeders لإنشاء البيانات التجريبية
C:\wamp64\bin\php\php8.3.6\php.exe artisan db:seed
```

### الخطوة 3: تشغيل الخادم
```bash
# تشغيل خادم Laravel
C:\wamp64\bin\php\php8.3.6\php.exe artisan serve

# أو استخدام WAMP
# ضع المشروع في C:\wamp64\www\
# ادخل على http://localhost/sector-management-system/public
```

### الخطوة 4: تسجيل الدخول
افتح المتصفح واذهب إلى: `http://localhost:8000`

**الحسابات التجريبية:**
- **مدير النظام**: <EMAIL> / password
- **مدير المنظمة**: <EMAIL> / password
- **مدير الموارد البشرية**: <EMAIL> / password

## 📋 قائمة التحقق السريع

- [ ] تم تثبيت PHP 8.3.6
- [ ] تم تثبيت MySQL
- [ ] تم إنشاء قاعدة البيانات
- [ ] تم تحديث ملف .env
- [ ] تم تشغيل migrate
- [ ] تم تشغيل db:seed
- [ ] تم تشغيل الخادم
- [ ] تم تسجيل الدخول بنجاح

## 🔧 حل المشاكل الشائعة

### مشكلة: خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل MySQL في WAMP
# تحقق من إعدادات .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sector_management_system
DB_USERNAME=root
DB_PASSWORD=
```

### مشكلة: خطأ في الصلاحيات
```bash
# تأكد من تشغيل الـ seeders
C:\wamp64\bin\php\php8.3.6\php.exe artisan db:seed --class=RolesAndPermissionsSeeder
```

### مشكلة: خطأ في تشغيل artisan
```bash
# تأكد من المسار الصحيح لـ PHP
# أو استخدم:
php artisan migrate
# إذا كان PHP في PATH
```

## 📱 الواجهات المتاحة

بعد تسجيل الدخول، ستجد:

### لوحة التحكم الرئيسية
- إحصائيات عامة
- رسوم بيانية
- الأنشطة الأخيرة
- المشاريع النشطة

### الوحدات المتاحة
1. **إدارة المنظمات** - عرض وتعديل بيانات المنظمة
2. **إدارة الموظفين** - قائمة الموظفين وملفاتهم
3. **إدارة المتطوعين** - تسجيل وإدارة المتطوعين
4. **إدارة المشاريع** - المشاريع والبرامج
5. **إدارة التبرعات** - التبرعات والمتبرعين
6. **التقارير** - تقارير مختلفة
7. **الإعدادات** - إعدادات النظام

## 🎯 الخطوات التالية

### للمطورين:
1. **إضافة Controllers جديدة** للوحدات المطلوبة
2. **تطوير Views** للواجهات
3. **إضافة Validations** للنماذج
4. **تطوير API** للتكامل الخارجي

### للمستخدمين:
1. **تخصيص بيانات المنظمة** في الإعدادات
2. **إضافة الموظفين** والمتطوعين
3. **إنشاء المشاريع** الأولى
4. **تسجيل التبرعات** الأولى

## 📊 البيانات التجريبية

النظام يأتي مع:
- **منظمة تجريبية**: جمعية الخير الخيرية
- **5 مستخدمين** بأدوار مختلفة
- **أدوار وصلاحيات** كاملة
- **هيكل قاعدة بيانات** جاهز

## 🔐 الأمان

تم تطبيق:
- **تشفير كلمات المرور**
- **حماية CSRF**
- **نظام صلاحيات متقدم**
- **فصل البيانات بين المنظمات**

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `storage/logs/laravel.log`
2. تأكد من تشغيل جميع الخدمات (Apache, MySQL)
3. راجع إعدادات .env

---

**مبروك! 🎉 النظام جاهز للاستخدام**

يمكنك الآن البدء في استخدام النظام وتخصيصه حسب احتياجاتك.
