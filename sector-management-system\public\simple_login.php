<?php
session_start();

// معالجة تسجيل الدخول
if ($_POST['email'] ?? false) {
    try {
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=sector_management_system;charset=utf8mb4', 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $email = $_POST['email'];
        $password = $_POST['password'];
        
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // للاختبار: قبول كلمة المرور "password" مباشرة أو التحقق من hash
        if ($user && ($password === 'password' || password_verify($password, $user['password']))) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['organization_id'] = $user['organization_id'];
            
            // تحديث وقت آخر دخول
            $stmt = $pdo->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
            $stmt->execute([$user['id']]);
            
            header('Location: dashboard.php');
            exit;
        } else {
            $error = "البريد الإلكتروني أو كلمة المرور غير صحيحة";
        }
    } catch (PDOException $e) {
        $error = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة مؤسسات القطاع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 3rem;
        }
        
        .login-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 3rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0">
            <!-- Login Form -->
            <div class="col-lg-6">
                <div class="login-form">
                    <div class="text-center mb-4">
                        <div class="logo">
                            <i class="fas fa-building"></i>
                        </div>
                        <h2 class="fw-bold text-dark">تسجيل الدخول</h2>
                        <p class="text-muted">مرحباً بك في نظام إدارة مؤسسات القطاع</p>
                    </div>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <input id="email" type="email" class="form-control" name="email" 
                                       value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" 
                                       required placeholder="أدخل بريدك الإلكتروني">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope text-muted"></i>
                                </span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <input id="password" type="password" class="form-control" name="password" 
                                       required placeholder="أدخل كلمة المرور">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                            </div>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <!-- Demo Accounts -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="fw-bold mb-2">حسابات تجريبية:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted d-block">مدير النظام:</small>
                                <small class="fw-bold demo-account" data-email="<EMAIL>"><EMAIL></small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted d-block">مدير المنظمة:</small>
                                <small class="fw-bold demo-account" data-email="<EMAIL>"><EMAIL></small>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <small class="text-muted d-block">مدير الموارد البشرية:</small>
                                <small class="fw-bold demo-account" data-email="<EMAIL>"><EMAIL></small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted d-block">مدير المشاريع:</small>
                                <small class="fw-bold demo-account" data-email="<EMAIL>"><EMAIL></small>
                            </div>
                        </div>
                        <small class="text-muted">كلمة المرور: password</small>
                    </div>
                </div>
            </div>

            <!-- Login Image -->
            <div class="col-lg-6 d-none d-lg-block">
                <div class="login-image">
                    <div>
                        <i class="fas fa-users fa-5x mb-4"></i>
                        <h3 class="fw-bold mb-3">نظام إدارة مؤسسات القطاع</h3>
                        <p class="lead mb-4">
                            نظام متكامل لإدارة المنظمات والجمعيات الخيرية
                        </p>
                        <div class="row text-center">
                            <div class="col-4">
                                <i class="fas fa-building fa-2x mb-2"></i>
                                <div class="small">إدارة المنظمات</div>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-project-diagram fa-2x mb-2"></i>
                                <div class="small">إدارة المشاريع</div>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-hand-holding-heart fa-2x mb-2"></i>
                                <div class="small">إدارة التبرعات</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Demo account quick fill
        document.querySelectorAll('.demo-account').forEach(function(element) {
            element.style.cursor = 'pointer';
            element.addEventListener('click', function() {
                document.getElementById('email').value = this.dataset.email;
                document.getElementById('password').value = 'password';
            });
        });
    </script>
</body>
</html>
