<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Donor extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'donor_code',
        'donor_type',
        'name_ar',
        'name_en',
        'national_id',
        'email',
        'phone',
        'address',
        'city',
        'country',
        'contact_person',
        'contact_person_title',
        'website',
        'total_donations',
        'donations_count',
        'first_donation_date',
        'last_donation_date',
        'category',
        'status',
        'rating',
        'preferred_causes',
        'anonymous_donations',
        'newsletter_subscription',
        'tax_receipt_required',
        'notes',
    ];

    protected $casts = [
        'total_donations' => 'decimal:2',
        'first_donation_date' => 'date',
        'last_donation_date' => 'date',
        'rating' => 'decimal:2',
        'preferred_causes' => 'array',
        'anonymous_donations' => 'boolean',
        'newsletter_subscription' => 'boolean',
        'tax_receipt_required' => 'boolean',
    ];

    // العلاقات
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function donations()
    {
        return $this->hasMany(Donation::class);
    }

    // الخصائص المحسوبة
    public function getNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    public function getIsIndividualAttribute()
    {
        return $this->donor_type === 'individual';
    }

    public function getIsCorporateAttribute()
    {
        return in_array($this->donor_type, ['corporate', 'foundation', 'government']);
    }

    public function getAverageDonationAmountAttribute()
    {
        return $this->donations_count > 0 ? round($this->total_donations / $this->donations_count, 2) : 0;
    }

    public function getDonationFrequencyAttribute()
    {
        if (!$this->first_donation_date || !$this->last_donation_date || $this->donations_count <= 1) {
            return 0;
        }

        $daysBetween = $this->first_donation_date->diffInDays($this->last_donation_date);
        return $daysBetween > 0 ? round($this->donations_count / ($daysBetween / 30), 2) : 0; // donations per month
    }

    public function getDaysSinceLastDonationAttribute()
    {
        return $this->last_donation_date ? $this->last_donation_date->diffInDays(now()) : null;
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('donor_type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeMajorDonors($query)
    {
        return $query->where('category', 'major');
    }

    public function scopeRegularDonors($query)
    {
        return $query->where('category', 'regular');
    }

    public function scopeRecentDonors($query, $days = 30)
    {
        return $query->where('last_donation_date', '>=', now()->subDays($days));
    }

    public function scopeInactiveDonors($query, $days = 365)
    {
        return $query->where('last_donation_date', '<', now()->subDays($days))
                    ->orWhereNull('last_donation_date');
    }

    public function scopeHighValueDonors($query, $minAmount = 10000)
    {
        return $query->where('total_donations', '>=', $minAmount);
    }

    // الطرق المساعدة
    public function updateDonationStats()
    {
        $donations = $this->donations()->where('status', 'confirmed');
        
        $this->total_donations = $donations->sum('amount');
        $this->donations_count = $donations->count();
        $this->first_donation_date = $donations->min('donation_date');
        $this->last_donation_date = $donations->max('donation_date');
        
        // تحديث فئة المتبرع بناءً على إجمالي التبرعات
        $this->updateCategory();
        
        $this->save();
    }

    public function updateCategory()
    {
        if ($this->total_donations >= 100000) {
            $this->category = 'major';
        } elseif ($this->total_donations >= 10000 || $this->donations_count >= 5) {
            $this->category = 'regular';
        } elseif ($this->donations_count >= 2) {
            $this->category = 'occasional';
        } else {
            $this->category = 'one_time';
        }
    }

    public function addDonation($amount, $donationData = [])
    {
        $donation = $this->donations()->create(array_merge($donationData, [
            'amount' => $amount,
            'organization_id' => $this->organization_id,
        ]));

        $this->updateDonationStats();

        return $donation;
    }

    public function getDonorTypes()
    {
        return [
            'individual' => 'فرد',
            'corporate' => 'شركة',
            'foundation' => 'مؤسسة',
            'government' => 'جهة حكومية',
        ];
    }

    public function getDonorCategories()
    {
        return [
            'major' => 'متبرع رئيسي',
            'regular' => 'متبرع منتظم',
            'occasional' => 'متبرع أحياناً',
            'one_time' => 'متبرع لمرة واحدة',
        ];
    }

    public function getPreferredCausesOptions()
    {
        return [
            'education' => 'التعليم',
            'health' => 'الصحة',
            'poverty_alleviation' => 'مكافحة الفقر',
            'disaster_relief' => 'الإغاثة',
            'orphan_care' => 'رعاية الأيتام',
            'elderly_care' => 'رعاية المسنين',
            'disability_support' => 'دعم ذوي الإعاقة',
            'environment' => 'البيئة',
            'community_development' => 'تنمية المجتمع',
            'women_empowerment' => 'تمكين المرأة',
            'youth_development' => 'تنمية الشباب',
            'religious_activities' => 'الأنشطة الدينية',
        ];
    }

    public function calculateLoyaltyScore()
    {
        $score = 0;

        // نقاط للمبلغ الإجمالي
        if ($this->total_donations >= 100000) {
            $score += 40;
        } elseif ($this->total_donations >= 50000) {
            $score += 30;
        } elseif ($this->total_donations >= 10000) {
            $score += 20;
        } else {
            $score += 10;
        }

        // نقاط لعدد التبرعات
        if ($this->donations_count >= 10) {
            $score += 30;
        } elseif ($this->donations_count >= 5) {
            $score += 20;
        } elseif ($this->donations_count >= 2) {
            $score += 10;
        }

        // نقاط للانتظام
        if ($this->donation_frequency >= 1) { // أكثر من تبرع شهرياً
            $score += 20;
        } elseif ($this->donation_frequency >= 0.5) { // تبرع كل شهرين
            $score += 15;
        } elseif ($this->donation_frequency >= 0.25) { // تبرع كل 4 أشهر
            $score += 10;
        }

        // نقاط للحداثة
        if ($this->days_since_last_donation <= 30) {
            $score += 10;
        } elseif ($this->days_since_last_donation <= 90) {
            $score += 5;
        }

        return min(100, $score);
    }
}
