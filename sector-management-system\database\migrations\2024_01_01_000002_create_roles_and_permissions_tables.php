<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول الأدوار
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->comment('اسم الدور');
            $table->string('display_name_ar')->comment('اسم الدور بالعربية');
            $table->string('display_name_en')->nullable()->comment('اسم الدور بالإنجليزية');
            $table->text('description_ar')->nullable()->comment('وصف الدور بالعربية');
            $table->text('description_en')->nullable()->comment('وصف الدور بالإنجليزية');
            $table->boolean('is_system_role')->default(false)->comment('دور نظام');
            $table->timestamps();
        });

        // جدول الصلاحيات
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->comment('اسم الصلاحية');
            $table->string('display_name_ar')->comment('اسم الصلاحية بالعربية');
            $table->string('display_name_en')->nullable()->comment('اسم الصلاحية بالإنجليزية');
            $table->text('description_ar')->nullable()->comment('وصف الصلاحية بالعربية');
            $table->text('description_en')->nullable()->comment('وصف الصلاحية بالإنجليزية');
            $table->string('module')->comment('الوحدة التي تنتمي إليها الصلاحية');
            $table->timestamps();
        });

        // جدول ربط الأدوار بالصلاحيات
        Schema::create('role_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained('roles')->onDelete('cascade');
            $table->foreignId('permission_id')->constrained('permissions')->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['role_id', 'permission_id']);
        });

        // جدول ربط المستخدمين بالأدوار
        Schema::create('user_roles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('role_id')->constrained('roles')->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained('organizations')->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['user_id', 'role_id', 'organization_id']);
        });

        // جدول الصلاحيات المباشرة للمستخدمين
        Schema::create('user_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('permission_id')->constrained('permissions')->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained('organizations')->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['user_id', 'permission_id', 'organization_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_permissions');
        Schema::dropIfExists('user_roles');
        Schema::dropIfExists('role_permissions');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('roles');
    }
};
