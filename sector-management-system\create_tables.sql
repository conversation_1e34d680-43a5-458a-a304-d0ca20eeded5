-- إنشاء جداول نظام إدارة مؤسسات القطاع
USE sector_management_system;

-- جدول المنظمات
CREATE TABLE IF NOT EXISTS organizations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL COMMENT 'اسم المنظمة بالعربية',
    name_en VARCHAR(255) NULL COMMENT 'اسم المنظمة بالإنجليزية',
    registration_number VARCHAR(255) UNIQUE NOT NULL COMMENT 'رقم التسجيل',
    type ENUM('charity', 'ngo', 'foundation', 'association') NOT NULL COMMENT 'نوع المنظمة',
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending' COMMENT 'حالة المنظمة',
    description_ar TEXT NULL COMMENT 'وصف المنظمة بالعربية',
    description_en TEXT NULL COMMENT 'وصف المنظمة بالإنجليزية',
    email VARCHAR(255) NULL,
    phone VARCHAR(255) NULL,
    website VARCHAR(255) NULL,
    address TEXT NULL COMMENT 'العنوان',
    city VARCHAR(255) NULL COMMENT 'المدينة',
    region VARCHAR(255) NULL COMMENT 'المنطقة',
    postal_code VARCHAR(255) NULL COMMENT 'الرمز البريدي',
    license_number VARCHAR(255) NULL COMMENT 'رقم الترخيص',
    license_issue_date DATE NULL COMMENT 'تاريخ إصدار الترخيص',
    license_expiry_date DATE NULL COMMENT 'تاريخ انتهاء الترخيص',
    license_authority VARCHAR(255) NULL COMMENT 'جهة الترخيص',
    annual_budget DECIMAL(15, 2) NULL COMMENT 'الميزانية السنوية',
    tax_number VARCHAR(255) NULL COMMENT 'الرقم الضريبي',
    establishment_date DATE NULL COMMENT 'تاريخ التأسيس',
    employees_count INT DEFAULT 0 COMMENT 'عدد الموظفين',
    volunteers_count INT DEFAULT 0 COMMENT 'عدد المتطوعين',
    services JSON NULL COMMENT 'الخدمات المقدمة',
    target_groups JSON NULL COMMENT 'الفئات المستهدفة',
    performance_score DECIMAL(3, 2) NULL COMMENT 'درجة تقييم الأداء',
    governance_level ENUM('excellent', 'good', 'fair', 'poor') NULL COMMENT 'مستوى الحوكمة',
    logo VARCHAR(255) NULL COMMENT 'شعار المنظمة',
    documents JSON NULL COMMENT 'الوثائق والملفات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_type_status (type, status),
    INDEX idx_city_region (city, region),
    INDEX idx_license_expiry (license_expiry_date)
);

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    first_name_ar VARCHAR(255) NULL,
    last_name_ar VARCHAR(255) NULL,
    first_name_en VARCHAR(255) NULL,
    last_name_en VARCHAR(255) NULL,
    phone VARCHAR(255) NULL,
    avatar VARCHAR(255) NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    language VARCHAR(2) DEFAULT 'ar',
    timezone VARCHAR(255) DEFAULT 'Asia/Riyadh',
    last_login_at TIMESTAMP NULL,
    organization_id BIGINT UNSIGNED NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE SET NULL,
    INDEX idx_status_organization (status, organization_id),
    INDEX idx_last_login (last_login_at)
);

-- جدول الأدوار
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL COMMENT 'اسم الدور',
    display_name_ar VARCHAR(255) NOT NULL COMMENT 'اسم الدور بالعربية',
    display_name_en VARCHAR(255) NULL COMMENT 'اسم الدور بالإنجليزية',
    description_ar TEXT NULL COMMENT 'وصف الدور بالعربية',
    description_en TEXT NULL COMMENT 'وصف الدور بالإنجليزية',
    is_system_role BOOLEAN DEFAULT FALSE COMMENT 'دور نظام',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL COMMENT 'اسم الصلاحية',
    display_name_ar VARCHAR(255) NOT NULL COMMENT 'اسم الصلاحية بالعربية',
    display_name_en VARCHAR(255) NULL COMMENT 'اسم الصلاحية بالإنجليزية',
    description_ar TEXT NULL COMMENT 'وصف الصلاحية بالعربية',
    description_en TEXT NULL COMMENT 'وصف الصلاحية بالإنجليزية',
    module VARCHAR(255) NOT NULL COMMENT 'الوحدة التي تنتمي إليها الصلاحية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول ربط الأدوار بالصلاحيات
CREATE TABLE IF NOT EXISTS role_permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    role_id BIGINT UNSIGNED NOT NULL,
    permission_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role_id, permission_id)
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE IF NOT EXISTS user_roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    role_id BIGINT UNSIGNED NOT NULL,
    organization_id BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role_org (user_id, role_id, organization_id)
);

-- جدول الصلاحيات المباشرة للمستخدمين
CREATE TABLE IF NOT EXISTS user_permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    permission_id BIGINT UNSIGNED NOT NULL,
    organization_id BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_permission_org (user_id, permission_id, organization_id)
);

-- إدراج البيانات الأساسية
INSERT INTO organizations (name_ar, name_en, registration_number, type, status, description_ar, description_en, email, phone, website, address, city, region, postal_code, license_number, license_issue_date, license_expiry_date, license_authority, annual_budget, tax_number, establishment_date, employees_count, volunteers_count, services, target_groups, performance_score, governance_level) VALUES
('جمعية الخير الخيرية', 'Al-Khair Charity Association', 'REG-2024-001', 'charity', 'active', 'جمعية خيرية تهدف إلى خدمة المجتمع وتقديم المساعدات للمحتاجين', 'A charity association aimed at serving the community and providing assistance to those in need', '<EMAIL>', '+966501234567', 'https://www.alkhair.org', 'شارع الملك فهد، الرياض', 'الرياض', 'منطقة الرياض', '12345', 'LIC-2024-001', '2024-01-01', '2026-12-31', 'وزارة الموارد البشرية والتنمية الاجتماعية', 5000000.00, '*********', '2020-01-01', 25, 100, '["التعليم", "الصحة", "الإغاثة", "رعاية الأيتام"]', '["الأيتام", "المسنين", "ذوي الإعاقة", "الأسر المحتاجة"]', 4.5, 'excellent');

-- إدراج المستخدمين
INSERT INTO users (name, email, password, first_name_ar, last_name_ar, first_name_en, last_name_en, phone, status, language, timezone, organization_id) VALUES
('مدير النظام', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', 'System', 'Administrator', '+966501234567', 'active', 'ar', 'Asia/Riyadh', NULL),
('مدير المنظمة', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد', 'المحمد', 'Ahmed', 'Al-Mohammed', '+966501234568', 'active', 'ar', 'Asia/Riyadh', 1),
('مدير الموارد البشرية', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة', 'العلي', 'Fatima', 'Al-Ali', '+966501234569', 'active', 'ar', 'Asia/Riyadh', 1),
('مدير المشاريع', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد', 'السعد', 'Mohammed', 'Al-Saad', '+966501234570', 'active', 'ar', 'Asia/Riyadh', 1),
('مدير المالية', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'خالد', 'الأحمد', 'Khalid', 'Al-Ahmad', '+966501234571', 'active', 'ar', 'Asia/Riyadh', 1);

SELECT 'تم إنشاء الجداول والبيانات الأساسية بنجاح!' AS status;
