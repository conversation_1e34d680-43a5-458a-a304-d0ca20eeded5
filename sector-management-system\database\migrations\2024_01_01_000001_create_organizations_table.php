<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar')->comment('اسم المنظمة بالعربية');
            $table->string('name_en')->nullable()->comment('اسم المنظمة بالإنجليزية');
            $table->string('registration_number')->unique()->comment('رقم التسجيل');
            $table->enum('type', ['charity', 'ngo', 'foundation', 'association'])->comment('نوع المنظمة');
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('pending')->comment('حالة المنظمة');
            $table->text('description_ar')->nullable()->comment('وصف المنظمة بالعربية');
            $table->text('description_en')->nullable()->comment('وصف المنظمة بالإنجليزية');
            
            // معلومات الاتصال
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('website')->nullable();
            $table->text('address')->nullable()->comment('العنوان');
            $table->string('city')->nullable()->comment('المدينة');
            $table->string('region')->nullable()->comment('المنطقة');
            $table->string('postal_code')->nullable()->comment('الرمز البريدي');
            
            // معلومات الترخيص
            $table->string('license_number')->nullable()->comment('رقم الترخيص');
            $table->date('license_issue_date')->nullable()->comment('تاريخ إصدار الترخيص');
            $table->date('license_expiry_date')->nullable()->comment('تاريخ انتهاء الترخيص');
            $table->string('license_authority')->nullable()->comment('جهة الترخيص');
            
            // معلومات مالية
            $table->decimal('annual_budget', 15, 2)->nullable()->comment('الميزانية السنوية');
            $table->string('tax_number')->nullable()->comment('الرقم الضريبي');
            
            // معلومات إضافية
            $table->date('establishment_date')->nullable()->comment('تاريخ التأسيس');
            $table->integer('employees_count')->default(0)->comment('عدد الموظفين');
            $table->integer('volunteers_count')->default(0)->comment('عدد المتطوعين');
            $table->json('services')->nullable()->comment('الخدمات المقدمة');
            $table->json('target_groups')->nullable()->comment('الفئات المستهدفة');
            
            // تقييم الأداء
            $table->decimal('performance_score', 3, 2)->nullable()->comment('درجة تقييم الأداء');
            $table->enum('governance_level', ['excellent', 'good', 'fair', 'poor'])->nullable()->comment('مستوى الحوكمة');
            
            // ملفات ووثائق
            $table->string('logo')->nullable()->comment('شعار المنظمة');
            $table->json('documents')->nullable()->comment('الوثائق والملفات');
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['type', 'status']);
            $table->index(['city', 'region']);
            $table->index('license_expiry_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizations');
    }
};
