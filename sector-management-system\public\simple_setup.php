<?php
// إعداد بسيط لقاعدة البيانات
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد بسيط - نظام إدارة مؤسسات القطاع</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Noto Sans Arabic', sans-serif; }</style>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-success text-white text-center'>";
echo "<h2><i class='fas fa-rocket'></i> إعداد سريع للنظام</h2>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // الاتصال بـ MySQL
    $pdo = new PDO('mysql:host=127.0.0.1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-info'>";
    echo "<h4>الخطوة 1: إنشاء قاعدة البيانات</h4>";
    
    // إنشاء قاعدة البيانات
    $sql = "CREATE DATABASE IF NOT EXISTS sector_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ تم إنشاء قاعدة البيانات</p>";
    echo "</div>";
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=sector_management_system;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-info'>";
    echo "<h4>الخطوة 2: إنشاء الجداول الأساسية</h4>";
    
    // جدول المنظمات
    $sql = "CREATE TABLE IF NOT EXISTS organizations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name_ar VARCHAR(191) NOT NULL,
        name_en VARCHAR(191) NULL,
        registration_number VARCHAR(100) UNIQUE NOT NULL,
        type ENUM('charity', 'ngo', 'foundation', 'association') NOT NULL,
        status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending',
        description_ar TEXT NULL,
        email VARCHAR(191) NULL,
        phone VARCHAR(50) NULL,
        website VARCHAR(191) NULL,
        address TEXT NULL,
        city VARCHAR(100) NULL,
        region VARCHAR(100) NULL,
        annual_budget DECIMAL(15, 2) NULL,
        employees_count INT DEFAULT 0,
        volunteers_count INT DEFAULT 0,
        performance_score DECIMAL(3, 2) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ جدول المنظمات</p>";
    
    // جدول المستخدمين
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(191) NOT NULL,
        email VARCHAR(191) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name_ar VARCHAR(100) NULL,
        last_name_ar VARCHAR(100) NULL,
        phone VARCHAR(50) NULL,
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        language VARCHAR(2) DEFAULT 'ar',
        last_login_at TIMESTAMP NULL,
        organization_id INT NULL,
        remember_token VARCHAR(100) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ جدول المستخدمين</p>";
    
    // جدول الأدوار
    $sql = "CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL,
        display_name_ar VARCHAR(191) NOT NULL,
        description_ar TEXT NULL,
        is_system_role BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ جدول الأدوار</p>";
    
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<h4>الخطوة 3: إدراج البيانات التجريبية</h4>";
    
    // إدراج منظمة تجريبية
    $sql = "INSERT IGNORE INTO organizations (name_ar, name_en, registration_number, type, status, description_ar, email, phone, website, address, city, region, annual_budget, employees_count, volunteers_count, performance_score) VALUES
    ('جمعية الخير الخيرية', 'Al-Khair Charity Association', 'REG-2024-001', 'charity', 'active', 'جمعية خيرية تهدف إلى خدمة المجتمع', '<EMAIL>', '+966501234567', 'https://www.alkhair.org', 'شارع الملك فهد، الرياض', 'الرياض', 'منطقة الرياض', 5000000.00, 25, 100, 4.5)";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ منظمة تجريبية</p>";
    
    // إدراج المستخدمين
    $sql = "INSERT IGNORE INTO users (name, email, password, first_name_ar, last_name_ar, phone, status, language, organization_id) VALUES
    ('مدير النظام', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', '+966501234567', 'active', 'ar', NULL),
    ('مدير المنظمة', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد', 'المحمد', '+966501234568', 'active', 'ar', 1),
    ('مدير الموارد البشرية', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة', 'العلي', '+966501234569', 'active', 'ar', 1),
    ('مدير المشاريع', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد', 'السعد', '+966501234570', 'active', 'ar', 1),
    ('مدير المالية', '<EMAIL>', '$2y$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'خالد', 'الأحمد', '+966501234571', 'active', 'ar', 1)";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ المستخدمين التجريبيين</p>";
    
    // إدراج الأدوار
    $sql = "INSERT IGNORE INTO roles (name, display_name_ar, description_ar, is_system_role) VALUES
    ('super_admin', 'مدير النظام', 'مدير النظام الرئيسي', TRUE),
    ('organization_admin', 'مدير المنظمة', 'مدير المنظمة', TRUE),
    ('hr_manager', 'مدير الموارد البشرية', 'مدير الموارد البشرية', TRUE),
    ('project_manager', 'مدير المشاريع', 'مدير المشاريع', TRUE),
    ('finance_manager', 'مدير المالية', 'مدير المالية', TRUE)";
    $pdo->exec($sql);
    echo "<p class='text-success'>✓ الأدوار الأساسية</p>";
    
    echo "</div>";
    
    echo "<div class='alert alert-success'>";
    echo "<h3><i class='fas fa-check-circle'></i> تم الإعداد بنجاح!</h3>";
    echo "<p>النظام جاهز للاستخدام مع الحسابات التالية:</p>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<ul class='list-unstyled'>";
    echo "<li><strong>مدير النظام:</strong><br><code><EMAIL></code></li>";
    echo "<li><strong>مدير المنظمة:</strong><br><code><EMAIL></code></li>";
    echo "<li><strong>مدير الموارد البشرية:</strong><br><code><EMAIL></code></li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<ul class='list-unstyled'>";
    echo "<li><strong>مدير المشاريع:</strong><br><code><EMAIL></code></li>";
    echo "<li><strong>مدير المالية:</strong><br><code><EMAIL></code></li>";
    echo "<li><strong>كلمة المرور للجميع:</strong><br><code>password</code></li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-exclamation-triangle'></i> خطأ في الإعداد</h4>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>يرجى التأكد من تشغيل خادم MySQL في WAMP</p>";
    echo "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='simple_login.php' class='btn btn-success btn-lg me-2'>";
echo "<i class='fas fa-sign-in-alt'></i> تسجيل الدخول";
echo "</a>";
echo "<a href='test.php' class='btn btn-secondary'>";
echo "<i class='fas fa-vial'></i> صفحة الاختبار";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js'></script>";
echo "</body>";
echo "</html>";
?>
