<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'first_name_ar',
        'last_name_ar',
        'first_name_en',
        'last_name_en',
        'phone',
        'avatar',
        'status',
        'language',
        'timezone',
        'last_login_at',
        'organization_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_login_at' => 'datetime',
        ];
    }

    // العلاقات
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_roles')->withPivot('organization_id');
    }

    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'user_permissions')->withPivot('organization_id');
    }

    public function employee()
    {
        return $this->hasOne(Employee::class);
    }

    public function volunteer()
    {
        return $this->hasOne(Volunteer::class);
    }

    // الخصائص المحسوبة
    public function getFullNameAttribute()
    {
        if (app()->getLocale() === 'ar') {
            return trim($this->first_name_ar . ' ' . $this->last_name_ar);
        }
        return trim(($this->first_name_en ?? $this->first_name_ar) . ' ' . ($this->last_name_en ?? $this->last_name_ar));
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    // طرق إدارة الصلاحيات
    public function hasRole($role, $organizationId = null)
    {
        if (is_string($role)) {
            return $this->roles()
                ->where('name', $role)
                ->when($organizationId, function ($query) use ($organizationId) {
                    return $query->wherePivot('organization_id', $organizationId);
                })
                ->exists();
        }

        return $this->roles()
            ->where('id', $role->id)
            ->when($organizationId, function ($query) use ($organizationId) {
                return $query->wherePivot('organization_id', $organizationId);
            })
            ->exists();
    }

    public function hasPermission($permission, $organizationId = null)
    {
        // التحقق من الصلاحيات المباشرة
        $directPermission = $this->permissions()
            ->where(is_string($permission) ? 'name' : 'id', is_string($permission) ? $permission : $permission->id)
            ->when($organizationId, function ($query) use ($organizationId) {
                return $query->wherePivot('organization_id', $organizationId);
            })
            ->exists();

        if ($directPermission) {
            return true;
        }

        // التحقق من الصلاحيات عبر الأدوار
        $roles = $this->roles()
            ->when($organizationId, function ($query) use ($organizationId) {
                return $query->wherePivot('organization_id', $organizationId);
            })
            ->get();

        foreach ($roles as $role) {
            if ($role->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    public function assignRole($role, $organizationId = null)
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->first();
        }

        if ($role && !$this->hasRole($role, $organizationId)) {
            $this->roles()->attach($role->id, ['organization_id' => $organizationId]);
        }

        return $this;
    }

    public function removeRole($role, $organizationId = null)
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->first();
        }

        if ($role) {
            $query = $this->roles()->where('role_id', $role->id);
            if ($organizationId) {
                $query->wherePivot('organization_id', $organizationId);
            }
            $query->detach();
        }

        return $this;
    }

    public function givePermissionTo($permission, $organizationId = null)
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->first();
        }

        if ($permission && !$this->hasPermission($permission, $organizationId)) {
            $this->permissions()->attach($permission->id, ['organization_id' => $organizationId]);
        }

        return $this;
    }

    public function revokePermissionTo($permission, $organizationId = null)
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->first();
        }

        if ($permission) {
            $query = $this->permissions()->where('permission_id', $permission->id);
            if ($organizationId) {
                $query->wherePivot('organization_id', $organizationId);
            }
            $query->detach();
        }

        return $this;
    }
}
