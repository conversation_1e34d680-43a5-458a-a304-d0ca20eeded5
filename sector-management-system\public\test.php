<?php
// اختبار بسيط للنظام
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار نظام إدارة مؤسسات القطاع</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Noto Sans Arabic', sans-serif; }</style>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h2><i class='fas fa-building'></i> نظام إدارة مؤسسات القطاع</h2>";
echo "</div>";
echo "<div class='card-body'>";

// اختبار PHP
echo "<div class='alert alert-success'>";
echo "<h4><i class='fas fa-check-circle'></i> اختبار PHP</h4>";
echo "<p>إصدار PHP: " . phpversion() . "</p>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

// اختبار قاعدة البيانات
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=sector_management_system;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-success'>";
    echo "<h4><i class='fas fa-database'></i> اختبار قاعدة البيانات</h4>";
    echo "<p>تم الاتصال بقاعدة البيانات بنجاح!</p>";
    
    // التحقق من وجود الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "<p>الجداول الموجودة: " . implode(', ', $tables) . "</p>";
    } else {
        echo "<p class='text-warning'>لا توجد جداول في قاعدة البيانات</p>";
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-exclamation-triangle'></i> خطأ في قاعدة البيانات</h4>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

// اختبار Laravel
echo "<div class='alert alert-info'>";
echo "<h4><i class='fas fa-code'></i> اختبار Laravel</h4>";
if (file_exists('../vendor/autoload.php')) {
    echo "<p class='text-success'>ملفات Laravel موجودة</p>";
    
    if (file_exists('../.env')) {
        echo "<p class='text-success'>ملف .env موجود</p>";
    } else {
        echo "<p class='text-warning'>ملف .env غير موجود</p>";
    }
    
    if (file_exists('../artisan')) {
        echo "<p class='text-success'>ملف artisan موجود</p>";
    } else {
        echo "<p class='text-warning'>ملف artisan غير موجود</p>";
    }
} else {
    echo "<p class='text-danger'>ملفات Laravel غير موجودة</p>";
}
echo "</div>";

// روابط مفيدة
echo "<div class='alert alert-light'>";
echo "<h4><i class='fas fa-link'></i> روابط مفيدة</h4>";
echo "<div class='d-grid gap-2'>";
echo "<a href='index.php' class='btn btn-primary'>الصفحة الرئيسية للنظام</a>";
echo "<a href='../public/index.php' class='btn btn-secondary'>Laravel Public</a>";
echo "<a href='create_database.php' class='btn btn-warning'>إنشاء قاعدة البيانات</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js'></script>";
echo "</body>";
echo "</html>";
?>
