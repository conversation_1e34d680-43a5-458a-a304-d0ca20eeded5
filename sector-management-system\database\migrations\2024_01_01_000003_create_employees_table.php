<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            
            // المعلومات الشخصية
            $table->string('employee_number')->unique()->comment('رقم الموظف');
            $table->string('first_name_ar')->comment('الاسم الأول بالعربية');
            $table->string('last_name_ar')->comment('اسم العائلة بالعربية');
            $table->string('first_name_en')->nullable()->comment('الاسم الأول بالإنجليزية');
            $table->string('last_name_en')->nullable()->comment('اسم العائلة بالإنجليزية');
            $table->string('national_id')->unique()->comment('رقم الهوية الوطنية');
            $table->date('birth_date')->comment('تاريخ الميلاد');
            $table->enum('gender', ['male', 'female'])->comment('الجنس');
            $table->enum('marital_status', ['single', 'married', 'divorced', 'widowed'])->comment('الحالة الاجتماعية');
            $table->string('nationality')->comment('الجنسية');
            
            // معلومات الاتصال
            $table->string('email')->unique();
            $table->string('phone')->comment('رقم الهاتف');
            $table->string('emergency_contact_name')->nullable()->comment('اسم جهة الاتصال في الطوارئ');
            $table->string('emergency_contact_phone')->nullable()->comment('رقم هاتف الطوارئ');
            $table->text('address')->nullable()->comment('العنوان');
            $table->string('city')->nullable()->comment('المدينة');
            
            // معلومات الوظيفة
            $table->string('job_title_ar')->comment('المسمى الوظيفي بالعربية');
            $table->string('job_title_en')->nullable()->comment('المسمى الوظيفي بالإنجليزية');
            $table->string('department')->comment('القسم');
            $table->foreignId('manager_id')->nullable()->constrained('employees')->onDelete('set null');
            $table->date('hire_date')->comment('تاريخ التوظيف');
            $table->date('contract_start_date')->comment('تاريخ بداية العقد');
            $table->date('contract_end_date')->nullable()->comment('تاريخ انتهاء العقد');
            $table->enum('employment_type', ['full_time', 'part_time', 'contract', 'intern'])->comment('نوع التوظيف');
            $table->enum('status', ['active', 'inactive', 'terminated', 'on_leave'])->default('active')->comment('حالة الموظف');
            
            // المعلومات المالية
            $table->decimal('basic_salary', 10, 2)->comment('الراتب الأساسي');
            $table->decimal('allowances', 10, 2)->default(0)->comment('البدلات');
            $table->decimal('deductions', 10, 2)->default(0)->comment('الخصومات');
            $table->string('bank_name')->nullable()->comment('اسم البنك');
            $table->string('bank_account_number')->nullable()->comment('رقم الحساب البنكي');
            $table->string('iban')->nullable()->comment('رقم الآيبان');
            
            // المؤهلات والخبرات
            $table->json('qualifications')->nullable()->comment('المؤهلات العلمية');
            $table->json('experiences')->nullable()->comment('الخبرات السابقة');
            $table->json('skills')->nullable()->comment('المهارات');
            $table->json('certifications')->nullable()->comment('الشهادات');
            
            // الإجازات والغياب
            $table->integer('annual_leave_balance')->default(0)->comment('رصيد الإجازة السنوية');
            $table->integer('sick_leave_balance')->default(0)->comment('رصيد الإجازة المرضية');
            
            // الملفات والوثائق
            $table->string('photo')->nullable()->comment('صورة الموظف');
            $table->json('documents')->nullable()->comment('الوثائق والملفات');
            
            // تقييم الأداء
            $table->decimal('performance_score', 3, 2)->nullable()->comment('درجة تقييم الأداء');
            $table->text('notes')->nullable()->comment('ملاحظات');
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['organization_id', 'status']);
            $table->index(['department', 'status']);
            $table->index('hire_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
