<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Volunteer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'user_id',
        'volunteer_number',
        'first_name_ar',
        'last_name_ar',
        'first_name_en',
        'last_name_en',
        'national_id',
        'birth_date',
        'gender',
        'marital_status',
        'nationality',
        'email',
        'phone',
        'emergency_contact_name',
        'emergency_contact_phone',
        'address',
        'city',
        'registration_date',
        'status',
        'areas_of_interest',
        'skills',
        'languages',
        'availability',
        'available_times',
        'qualifications',
        'experiences',
        'certifications',
        'training_courses',
        'health_conditions',
        'has_criminal_record',
        'criminal_record_details',
        'total_volunteer_hours',
        'completed_activities',
        'rating',
        'no_show_count',
        'awards',
        'achievements',
        'points',
        'preferences',
        'newsletter_subscription',
        'sms_notifications',
        'email_notifications',
        'photo',
        'documents',
        'motivation',
        'notes',
        'feedback',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'registration_date' => 'date',
        'areas_of_interest' => 'array',
        'skills' => 'array',
        'languages' => 'array',
        'available_times' => 'array',
        'qualifications' => 'array',
        'experiences' => 'array',
        'certifications' => 'array',
        'training_courses' => 'array',
        'health_conditions' => 'array',
        'awards' => 'array',
        'achievements' => 'array',
        'preferences' => 'array',
        'documents' => 'array',
        'has_criminal_record' => 'boolean',
        'newsletter_subscription' => 'boolean',
        'sms_notifications' => 'boolean',
        'email_notifications' => 'boolean',
        'rating' => 'decimal:2',
    ];

    // العلاقات
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // الخصائص المحسوبة
    public function getFullNameAttribute()
    {
        if (app()->getLocale() === 'ar') {
            return trim($this->first_name_ar . ' ' . $this->last_name_ar);
        }
        return trim(($this->first_name_en ?? $this->first_name_ar) . ' ' . ($this->last_name_en ?? $this->last_name_ar));
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    public function getAgeAttribute()
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    public function getVolunteeringSinceAttribute()
    {
        return $this->registration_date ? $this->registration_date->diffForHumans() : null;
    }

    public function getAverageHoursPerMonthAttribute()
    {
        $monthsSinceRegistration = $this->registration_date ? $this->registration_date->diffInMonths(now()) : 0;
        return $monthsSinceRegistration > 0 ? round($this->total_volunteer_hours / $monthsSinceRegistration, 2) : 0;
    }

    public function getReliabilityScoreAttribute()
    {
        if ($this->completed_activities == 0) {
            return 0;
        }
        
        $totalActivities = $this->completed_activities + $this->no_show_count;
        return round(($this->completed_activities / $totalActivities) * 100, 2);
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    public function scopeBySkill($query, $skill)
    {
        return $query->whereJsonContains('skills', $skill);
    }

    public function scopeByAreaOfInterest($query, $area)
    {
        return $query->whereJsonContains('areas_of_interest', $area);
    }

    public function scopeAvailableOn($query, $day)
    {
        return $query->where(function ($q) use ($day) {
            $q->where('availability', 'both')
              ->orWhere('availability', 'flexible')
              ->orWhere(function ($subQ) use ($day) {
                  if (in_array($day, ['saturday', 'sunday', 'friday'])) {
                      $subQ->where('availability', 'weekends');
                  } else {
                      $subQ->where('availability', 'weekdays');
                  }
              });
        });
    }

    public function scopeHighRated($query, $minRating = 4.0)
    {
        return $query->where('rating', '>=', $minRating);
    }

    // الطرق المساعدة
    public function addVolunteerHours($hours, $activityDescription = null)
    {
        $this->total_volunteer_hours += $hours;
        $this->save();

        // يمكن إضافة سجل تفصيلي للأنشطة هنا
    }

    public function completeActivity()
    {
        $this->completed_activities++;
        $this->save();
    }

    public function recordNoShow()
    {
        $this->no_show_count++;
        $this->save();
    }

    public function addPoints($points, $reason = null)
    {
        $this->points += $points;
        $this->save();
    }

    public function giveAward($award)
    {
        $awards = $this->awards ?? [];
        $awards[] = [
            'title' => $award,
            'date' => now()->toDateString(),
        ];
        $this->awards = $awards;
        $this->save();
    }

    public function updateRating($newRating)
    {
        // يمكن تحسين هذا ليكون متوسط التقييمات
        $this->rating = $newRating;
        $this->save();
    }

    public function getAreasOfInterestOptions()
    {
        return [
            'education' => 'التعليم',
            'health' => 'الصحة',
            'environment' => 'البيئة',
            'social_services' => 'الخدمات الاجتماعية',
            'elderly_care' => 'رعاية المسنين',
            'children_care' => 'رعاية الأطفال',
            'disability_support' => 'دعم ذوي الإعاقة',
            'disaster_relief' => 'الإغاثة',
            'community_development' => 'تنمية المجتمع',
            'arts_culture' => 'الفنون والثقافة',
            'sports' => 'الرياضة',
            'technology' => 'التكنولوجيا',
        ];
    }

    public function getSkillsOptions()
    {
        return [
            'communication' => 'التواصل',
            'leadership' => 'القيادة',
            'teaching' => 'التدريس',
            'counseling' => 'الإرشاد',
            'first_aid' => 'الإسعافات الأولية',
            'computer_skills' => 'مهارات الحاسوب',
            'languages' => 'اللغات',
            'driving' => 'القيادة',
            'cooking' => 'الطبخ',
            'crafts' => 'الحرف اليدوية',
            'photography' => 'التصوير',
            'writing' => 'الكتابة',
            'event_planning' => 'تنظيم الفعاليات',
            'fundraising' => 'جمع التبرعات',
        ];
    }
}
