<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // إدارة المستخدمين
            ['name' => 'users.view', 'display_name_ar' => 'عرض المستخدمين', 'display_name_en' => 'View Users', 'module' => 'users'],
            ['name' => 'users.create', 'display_name_ar' => 'إنشاء مستخدم', 'display_name_en' => 'Create User', 'module' => 'users'],
            ['name' => 'users.edit', 'display_name_ar' => 'تعديل المستخدمين', 'display_name_en' => 'Edit Users', 'module' => 'users'],
            ['name' => 'users.delete', 'display_name_ar' => 'حذف المستخدمين', 'display_name_en' => 'Delete Users', 'module' => 'users'],
            ['name' => 'users.manage_roles', 'display_name_ar' => 'إدارة أدوار المستخدمين', 'display_name_en' => 'Manage User Roles', 'module' => 'users'],

            // إدارة المنظمات
            ['name' => 'organizations.view', 'display_name_ar' => 'عرض المنظمات', 'display_name_en' => 'View Organizations', 'module' => 'organizations'],
            ['name' => 'organizations.create', 'display_name_ar' => 'إنشاء منظمة', 'display_name_en' => 'Create Organization', 'module' => 'organizations'],
            ['name' => 'organizations.edit', 'display_name_ar' => 'تعديل المنظمات', 'display_name_en' => 'Edit Organizations', 'module' => 'organizations'],
            ['name' => 'organizations.delete', 'display_name_ar' => 'حذف المنظمات', 'display_name_en' => 'Delete Organizations', 'module' => 'organizations'],
            ['name' => 'organizations.approve', 'display_name_ar' => 'الموافقة على المنظمات', 'display_name_en' => 'Approve Organizations', 'module' => 'organizations'],

            // إدارة الموظفين
            ['name' => 'employees.view', 'display_name_ar' => 'عرض الموظفين', 'display_name_en' => 'View Employees', 'module' => 'employees'],
            ['name' => 'employees.create', 'display_name_ar' => 'إضافة موظف', 'display_name_en' => 'Create Employee', 'module' => 'employees'],
            ['name' => 'employees.edit', 'display_name_ar' => 'تعديل الموظفين', 'display_name_en' => 'Edit Employees', 'module' => 'employees'],
            ['name' => 'employees.delete', 'display_name_ar' => 'حذف الموظفين', 'display_name_en' => 'Delete Employees', 'module' => 'employees'],
            ['name' => 'employees.manage_payroll', 'display_name_ar' => 'إدارة الرواتب', 'display_name_en' => 'Manage Payroll', 'module' => 'employees'],

            // إدارة المتطوعين
            ['name' => 'volunteers.view', 'display_name_ar' => 'عرض المتطوعين', 'display_name_en' => 'View Volunteers', 'module' => 'volunteers'],
            ['name' => 'volunteers.create', 'display_name_ar' => 'إضافة متطوع', 'display_name_en' => 'Create Volunteer', 'module' => 'volunteers'],
            ['name' => 'volunteers.edit', 'display_name_ar' => 'تعديل المتطوعين', 'display_name_en' => 'Edit Volunteers', 'module' => 'volunteers'],
            ['name' => 'volunteers.delete', 'display_name_ar' => 'حذف المتطوعين', 'display_name_en' => 'Delete Volunteers', 'module' => 'volunteers'],
            ['name' => 'volunteers.assign_activities', 'display_name_ar' => 'تعيين الأنشطة', 'display_name_en' => 'Assign Activities', 'module' => 'volunteers'],

            // إدارة المشاريع
            ['name' => 'projects.view', 'display_name_ar' => 'عرض المشاريع', 'display_name_en' => 'View Projects', 'module' => 'projects'],
            ['name' => 'projects.create', 'display_name_ar' => 'إنشاء مشروع', 'display_name_en' => 'Create Project', 'module' => 'projects'],
            ['name' => 'projects.edit', 'display_name_ar' => 'تعديل المشاريع', 'display_name_en' => 'Edit Projects', 'module' => 'projects'],
            ['name' => 'projects.delete', 'display_name_ar' => 'حذف المشاريع', 'display_name_en' => 'Delete Projects', 'module' => 'projects'],
            ['name' => 'projects.manage_budget', 'display_name_ar' => 'إدارة ميزانية المشاريع', 'display_name_en' => 'Manage Project Budget', 'module' => 'projects'],

            // إدارة التبرعات
            ['name' => 'donations.view', 'display_name_ar' => 'عرض التبرعات', 'display_name_en' => 'View Donations', 'module' => 'donations'],
            ['name' => 'donations.create', 'display_name_ar' => 'إضافة تبرع', 'display_name_en' => 'Create Donation', 'module' => 'donations'],
            ['name' => 'donations.edit', 'display_name_ar' => 'تعديل التبرعات', 'display_name_en' => 'Edit Donations', 'module' => 'donations'],
            ['name' => 'donations.delete', 'display_name_ar' => 'حذف التبرعات', 'display_name_en' => 'Delete Donations', 'module' => 'donations'],
            ['name' => 'donations.approve', 'display_name_ar' => 'الموافقة على التبرعات', 'display_name_en' => 'Approve Donations', 'module' => 'donations'],

            // إدارة المالية
            ['name' => 'finance.view', 'display_name_ar' => 'عرض المالية', 'display_name_en' => 'View Finance', 'module' => 'finance'],
            ['name' => 'finance.create_entries', 'display_name_ar' => 'إنشاء قيود محاسبية', 'display_name_en' => 'Create Accounting Entries', 'module' => 'finance'],
            ['name' => 'finance.approve_entries', 'display_name_ar' => 'الموافقة على القيود', 'display_name_en' => 'Approve Entries', 'module' => 'finance'],
            ['name' => 'finance.view_reports', 'display_name_ar' => 'عرض التقارير المالية', 'display_name_en' => 'View Financial Reports', 'module' => 'finance'],
            ['name' => 'finance.manage_budget', 'display_name_ar' => 'إدارة الميزانية', 'display_name_en' => 'Manage Budget', 'module' => 'finance'],

            // التقارير
            ['name' => 'reports.view', 'display_name_ar' => 'عرض التقارير', 'display_name_en' => 'View Reports', 'module' => 'reports'],
            ['name' => 'reports.create', 'display_name_ar' => 'إنشاء تقارير', 'display_name_en' => 'Create Reports', 'module' => 'reports'],
            ['name' => 'reports.export', 'display_name_ar' => 'تصدير التقارير', 'display_name_en' => 'Export Reports', 'module' => 'reports'],
            ['name' => 'reports.financial', 'display_name_ar' => 'التقارير المالية', 'display_name_en' => 'Financial Reports', 'module' => 'reports'],

            // الإعدادات
            ['name' => 'settings.view', 'display_name_ar' => 'عرض الإعدادات', 'display_name_en' => 'View Settings', 'module' => 'settings'],
            ['name' => 'settings.edit', 'display_name_ar' => 'تعديل الإعدادات', 'display_name_en' => 'Edit Settings', 'module' => 'settings'],
            ['name' => 'settings.system', 'display_name_ar' => 'إعدادات النظام', 'display_name_en' => 'System Settings', 'module' => 'settings'],
            ['name' => 'settings.backup', 'display_name_ar' => 'النسخ الاحتياطي', 'display_name_en' => 'Backup', 'module' => 'settings'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission['name']], $permission);
        }

        // إنشاء الأدوار
        $roles = [
            [
                'name' => 'super_admin',
                'display_name_ar' => 'مدير النظام',
                'display_name_en' => 'Super Administrator',
                'description_ar' => 'مدير النظام الرئيسي مع جميع الصلاحيات',
                'description_en' => 'Main system administrator with all permissions',
                'is_system_role' => true,
            ],
            [
                'name' => 'organization_admin',
                'display_name_ar' => 'مدير المنظمة',
                'display_name_en' => 'Organization Administrator',
                'description_ar' => 'مدير المنظمة مع صلاحيات إدارة المنظمة',
                'description_en' => 'Organization administrator with organization management permissions',
                'is_system_role' => true,
            ],
            [
                'name' => 'hr_manager',
                'display_name_ar' => 'مدير الموارد البشرية',
                'display_name_en' => 'HR Manager',
                'description_ar' => 'مدير الموارد البشرية مع صلاحيات إدارة الموظفين والمتطوعين',
                'description_en' => 'HR manager with employee and volunteer management permissions',
                'is_system_role' => true,
            ],
            [
                'name' => 'project_manager',
                'display_name_ar' => 'مدير المشاريع',
                'display_name_en' => 'Project Manager',
                'description_ar' => 'مدير المشاريع مع صلاحيات إدارة المشاريع والبرامج',
                'description_en' => 'Project manager with project and program management permissions',
                'is_system_role' => true,
            ],
            [
                'name' => 'finance_manager',
                'display_name_ar' => 'مدير المالية',
                'display_name_en' => 'Finance Manager',
                'description_ar' => 'مدير المالية مع صلاحيات إدارة الشؤون المالية والتبرعات',
                'description_en' => 'Finance manager with financial and donation management permissions',
                'is_system_role' => true,
            ],
            [
                'name' => 'employee',
                'display_name_ar' => 'موظف',
                'display_name_en' => 'Employee',
                'description_ar' => 'موظف عادي مع صلاحيات محدودة',
                'description_en' => 'Regular employee with limited permissions',
                'is_system_role' => true,
            ],
            [
                'name' => 'volunteer_coordinator',
                'display_name_ar' => 'منسق المتطوعين',
                'display_name_en' => 'Volunteer Coordinator',
                'description_ar' => 'منسق المتطوعين مع صلاحيات إدارة المتطوعين',
                'description_en' => 'Volunteer coordinator with volunteer management permissions',
                'is_system_role' => true,
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(['name' => $roleData['name']], $roleData);

            // تعيين الصلاحيات للأدوار
            switch ($roleData['name']) {
                case 'super_admin':
                    $role->syncPermissions(Permission::all());
                    break;

                case 'organization_admin':
                    $role->syncPermissions(Permission::whereIn('module', [
                        'users', 'employees', 'volunteers', 'projects', 'donations', 'finance', 'reports', 'settings'
                    ])->get());
                    break;

                case 'hr_manager':
                    $role->syncPermissions(Permission::whereIn('module', [
                        'employees', 'volunteers'
                    ])->get());
                    break;

                case 'project_manager':
                    $role->syncPermissions(Permission::whereIn('module', [
                        'projects', 'volunteers'
                    ])->where('name', 'not like', '%.delete')->get());
                    break;

                case 'finance_manager':
                    $role->syncPermissions(Permission::whereIn('module', [
                        'donations', 'finance', 'reports'
                    ])->get());
                    break;

                case 'volunteer_coordinator':
                    $role->syncPermissions(Permission::where('module', 'volunteers')
                        ->where('name', 'not like', '%.delete')->get());
                    break;

                case 'employee':
                    $role->syncPermissions(Permission::whereIn('name', [
                        'projects.view', 'volunteers.view', 'reports.view'
                    ])->get());
                    break;
            }
        }
    }
}
