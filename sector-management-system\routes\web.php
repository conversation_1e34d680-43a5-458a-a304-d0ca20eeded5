<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\VolunteerController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\DonationController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;

// الصفحة الرئيسية
Route::get('/', function () {
    return redirect()->route('login');
});

// مسارات المصادقة
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
});

Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// المسارات المحمية
Route::middleware(['auth'])->group(function () {

    // لوحة التحكم
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/chart-data', [DashboardController::class, 'getChartData'])->name('dashboard.chart-data');

    // إدارة المستخدمين
    Route::middleware(['permission:users.view'])->group(function () {
        Route::resource('users', UserController::class);
        Route::post('users/{user}/assign-role', [UserController::class, 'assignRole'])->name('users.assign-role');
        Route::delete('users/{user}/remove-role/{role}', [UserController::class, 'removeRole'])->name('users.remove-role');
    });

    // إدارة الأدوار والصلاحيات
    Route::middleware(['permission:settings.system'])->group(function () {
        Route::resource('roles', RoleController::class);
        Route::post('roles/{role}/permissions', [RoleController::class, 'updatePermissions'])->name('roles.permissions');
    });

    // إدارة المنظمات
    Route::middleware(['permission:organizations.view'])->group(function () {
        Route::resource('organizations', OrganizationController::class);
        Route::post('organizations/{organization}/approve', [OrganizationController::class, 'approve'])->name('organizations.approve');
        Route::post('organizations/{organization}/suspend', [OrganizationController::class, 'suspend'])->name('organizations.suspend');
    });

    // إدارة الموظفين
    Route::middleware(['permission:employees.view'])->group(function () {
        Route::resource('employees', EmployeeController::class);
        Route::get('employees/{employee}/profile', [EmployeeController::class, 'profile'])->name('employees.profile');
        Route::post('employees/{employee}/update-salary', [EmployeeController::class, 'updateSalary'])->name('employees.update-salary');
        Route::post('employees/{employee}/leave-request', [EmployeeController::class, 'leaveRequest'])->name('employees.leave-request');
    });

    // إدارة المتطوعين
    Route::middleware(['permission:volunteers.view'])->group(function () {
        Route::resource('volunteers', VolunteerController::class);
        Route::get('volunteers/{volunteer}/profile', [VolunteerController::class, 'profile'])->name('volunteers.profile');
        Route::post('volunteers/{volunteer}/add-hours', [VolunteerController::class, 'addHours'])->name('volunteers.add-hours');
        Route::post('volunteers/{volunteer}/assign-activity', [VolunteerController::class, 'assignActivity'])->name('volunteers.assign-activity');
    });

    // إدارة المشاريع
    Route::middleware(['permission:projects.view'])->group(function () {
        Route::resource('projects', ProjectController::class);
        Route::get('projects/{project}/dashboard', [ProjectController::class, 'dashboard'])->name('projects.dashboard');
        Route::post('projects/{project}/update-progress', [ProjectController::class, 'updateProgress'])->name('projects.update-progress');
        Route::post('projects/{project}/add-expense', [ProjectController::class, 'addExpense'])->name('projects.add-expense');
    });

    // إدارة التبرعات
    Route::middleware(['permission:donations.view'])->group(function () {
        Route::resource('donations', DonationController::class);
        Route::post('donations/{donation}/approve', [DonationController::class, 'approve'])->name('donations.approve');
        Route::post('donations/{donation}/process', [DonationController::class, 'process'])->name('donations.process');
        Route::get('donations/{donation}/receipt', [DonationController::class, 'receipt'])->name('donations.receipt');
    });

    // التقارير
    Route::middleware(['permission:reports.view'])->group(function () {
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', function () {
                return view('reports.index');
            })->name('index');

            Route::get('/organizations', function () {
                return view('reports.organizations');
            })->name('organizations');

            Route::get('/employees', function () {
                return view('reports.employees');
            })->name('employees');

            Route::get('/volunteers', function () {
                return view('reports.volunteers');
            })->name('volunteers');

            Route::get('/projects', function () {
                return view('reports.projects');
            })->name('projects');

            Route::get('/donations', function () {
                return view('reports.donations');
            })->name('donations');

            Route::get('/financial', function () {
                return view('reports.financial');
            })->name('financial');
        });
    });

    // الإعدادات
    Route::middleware(['permission:settings.view'])->group(function () {
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', function () {
                return view('settings.index');
            })->name('index');

            Route::get('/general', function () {
                return view('settings.general');
            })->name('general');

            Route::get('/notifications', function () {
                return view('settings.notifications');
            })->name('notifications');

            Route::get('/backup', function () {
                return view('settings.backup');
            })->name('backup');
        });
    });

    // الملف الشخصي
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', function () {
            return view('profile.index');
        })->name('index');

        Route::put('/update', function () {
            // تحديث الملف الشخصي
        })->name('update');

        Route::put('/password', function () {
            // تغيير كلمة المرور
        })->name('password');
    });
});
