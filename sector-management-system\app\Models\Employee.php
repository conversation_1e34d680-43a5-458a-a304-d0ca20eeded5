<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Employee extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'user_id',
        'employee_number',
        'first_name_ar',
        'last_name_ar',
        'first_name_en',
        'last_name_en',
        'national_id',
        'birth_date',
        'gender',
        'marital_status',
        'nationality',
        'email',
        'phone',
        'emergency_contact_name',
        'emergency_contact_phone',
        'address',
        'city',
        'job_title_ar',
        'job_title_en',
        'department',
        'manager_id',
        'hire_date',
        'contract_start_date',
        'contract_end_date',
        'employment_type',
        'status',
        'basic_salary',
        'allowances',
        'deductions',
        'bank_name',
        'bank_account_number',
        'iban',
        'qualifications',
        'experiences',
        'skills',
        'certifications',
        'annual_leave_balance',
        'sick_leave_balance',
        'photo',
        'documents',
        'performance_score',
        'notes',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'hire_date' => 'date',
        'contract_start_date' => 'date',
        'contract_end_date' => 'date',
        'basic_salary' => 'decimal:2',
        'allowances' => 'decimal:2',
        'deductions' => 'decimal:2',
        'performance_score' => 'decimal:2',
        'qualifications' => 'array',
        'experiences' => 'array',
        'skills' => 'array',
        'certifications' => 'array',
        'documents' => 'array',
    ];

    // العلاقات
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function manager()
    {
        return $this->belongsTo(Employee::class, 'manager_id');
    }

    public function subordinates()
    {
        return $this->hasMany(Employee::class, 'manager_id');
    }

    // الخصائص المحسوبة
    public function getFullNameAttribute()
    {
        if (app()->getLocale() === 'ar') {
            return trim($this->first_name_ar . ' ' . $this->last_name_ar);
        }
        return trim(($this->first_name_en ?? $this->first_name_ar) . ' ' . ($this->last_name_en ?? $this->last_name_ar));
    }

    public function getJobTitleAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->job_title_ar : ($this->job_title_en ?? $this->job_title_ar);
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    public function getAgeAttribute()
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    public function getYearsOfServiceAttribute()
    {
        return $this->hire_date ? $this->hire_date->diffInYears(now()) : 0;
    }

    public function getTotalSalaryAttribute()
    {
        return $this->basic_salary + $this->allowances - $this->deductions;
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeManagers($query)
    {
        return $query->whereNotNull('manager_id');
    }

    // الطرق المساعدة
    public function calculateAnnualLeaveEntitlement()
    {
        // حساب استحقاق الإجازة السنوية بناءً على سنوات الخدمة
        $yearsOfService = $this->years_of_service;
        
        if ($yearsOfService < 1) {
            return 21; // 21 يوم للسنة الأولى
        } elseif ($yearsOfService < 5) {
            return 30; // 30 يوم للسنوات 1-5
        } else {
            return 45; // 45 يوم للسنوات 5+
        }
    }

    public function updateLeaveBalance()
    {
        $entitlement = $this->calculateAnnualLeaveEntitlement();
        $this->annual_leave_balance = $entitlement;
        $this->save();
    }

    public function isContractExpiring($days = 30)
    {
        return $this->contract_end_date && 
               $this->contract_end_date->diffInDays(now()) <= $days &&
               $this->contract_end_date->isFuture();
    }

    public function getDepartments()
    {
        return [
            'administration' => 'الإدارة',
            'finance' => 'المالية',
            'hr' => 'الموارد البشرية',
            'projects' => 'المشاريع',
            'programs' => 'البرامج',
            'marketing' => 'التسويق',
            'it' => 'تقنية المعلومات',
            'legal' => 'الشؤون القانونية',
            'operations' => 'العمليات',
        ];
    }
}
