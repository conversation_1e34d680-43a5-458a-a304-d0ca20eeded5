<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // إضافة الحقول الجديدة
            $table->string('first_name_ar')->nullable()->after('name');
            $table->string('last_name_ar')->nullable()->after('first_name_ar');
            $table->string('first_name_en')->nullable()->after('last_name_ar');
            $table->string('last_name_en')->nullable()->after('first_name_en');
            $table->string('phone')->nullable()->after('email');
            $table->string('avatar')->nullable()->after('phone');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('avatar');
            $table->string('language', 2)->default('ar')->after('status');
            $table->string('timezone')->default('Asia/Riyadh')->after('language');
            $table->timestamp('last_login_at')->nullable()->after('timezone');
            $table->foreignId('organization_id')->nullable()->after('last_login_at')->constrained('organizations')->onDelete('set null');
            
            // إضافة soft deletes
            $table->softDeletes()->after('updated_at');
            
            // إضافة فهارس
            $table->index(['status', 'organization_id']);
            $table->index('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropForeign(['organization_id']);
            $table->dropColumn([
                'first_name_ar',
                'last_name_ar',
                'first_name_en',
                'last_name_en',
                'phone',
                'avatar',
                'status',
                'language',
                'timezone',
                'last_login_at',
                'organization_id',
            ]);
        });
    }
};
