# نظام إدارة مؤسسات القطاع

نظام سحابي متكامل لإدارة مؤسسات القطاع الخيري والتنموي، مطور باستخدام Laravel و MySQL مع دعم تعدد المستخدمين والصلاحيات.

## 🌟 الميزات الرئيسية

### الوحدات الأساسية
- **إدارة المستخدمين والصلاحيات** - نظام أدوار وصلاحيات متقدم
- **إدارة الجمعيات والمنظمات** - البيانات الأساسية، الترخيص، التقييم
- **إدارة الموظفين والموارد البشرية** - ملفات شاملة للموظفين
- **إدارة المشاريع والبرامج** - تتبع المشاريع والميزانيات
- **إدارة المتطوعين** - تسجيل وإدارة المتطوعين
- **إدارة التبرعات** - تبرعات نقدية وعينية
- **إدارة المستودعات والمخزون** - تتبع المواد والمعدات
- **إدارة الشؤون المالية** - قيود محاسبية وتقارير مالية
- **إدارة الاجتماعات والتقارير** - جدولة وتوثيق الاجتماعات
- **إدارة العقود والوثائق** - أرشفة إلكترونية للوثائق

### الخصائص التقنية
- **واجهة مستخدم حديثة** - Bootstrap مع تصميم متجاوب
- **دعم رفع الملفات** - PDF، Word، صور
- **نظام إشعارات داخلي** - تنبيهات فورية
- **سجل التدقيق** - تتبع جميع العمليات
- **لوحة تحكم متقدمة** - مخصصة حسب الصلاحيات
- **نظام دخول آمن** - مصادقة متقدمة

### التحول الرقمي
- **إدارة المستندات إلكترونياً** - أرشفة ذكية
- **توقيع إلكتروني داخلي** - سير عمل الموافقات
- **تكامل البريد الإلكتروني** - إشعارات تلقائية
- **تقارير ولوحات بيانات ديناميكية** - رؤى تحليلية

### خيارات إضافية
- **دعم اللغة العربية والإنجليزية** - واجهة متعددة اللغات
- **واجهة API كاملة** - للربط مع أنظمة خارجية
- **نظام تقييم الأداء** - مؤشرات الحوكمة
- **نظام حجز مواعيد** - للزوار والمراجعين

## 🚀 التثبيت والإعداد

### المتطلبات
- PHP 8.2 أو أحدث
- MySQL 8.0 أو أحدث
- Composer
- Node.js و npm

### خطوات التثبيت

1. **إعداد قاعدة البيانات**
```sql
CREATE DATABASE sector_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **تحديث ملف .env**
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sector_management_system
DB_USERNAME=root
DB_PASSWORD=
```

3. **تشغيل الـ Migrations والـ Seeders**
```bash
php artisan migrate
php artisan db:seed
```

4. **تشغيل الخادم**
```bash
php artisan serve
```

## 👥 الحسابات التجريبية

بعد تشغيل الـ Seeders، ستتوفر الحسابات التالية:

| الدور | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| مدير النظام | <EMAIL> | password |
| مدير المنظمة | <EMAIL> | password |
| مدير الموارد البشرية | <EMAIL> | password |
| مدير المشاريع | <EMAIL> | password |
| مدير المالية | <EMAIL> | password |

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية
- `organizations` - بيانات المنظمات
- `users` - المستخدمين
- `roles` - الأدوار
- `permissions` - الصلاحيات
- `employees` - الموظفين
- `volunteers` - المتطوعين
- `projects` - المشاريع
- `donors` - المتبرعين
- `donations` - التبرعات

## 🔐 نظام الصلاحيات

النظام يدعم نظام صلاحيات متقدم مع:
- **أدوار النظام**: مدير النظام، مدير المنظمة، مدير الموارد البشرية، إلخ
- **صلاحيات مفصلة**: عرض، إنشاء، تعديل، حذف لكل وحدة
- **صلاحيات على مستوى المنظمة**: كل مستخدم يرى بيانات منظمته فقط

## 🛡️ الأمان

- **تشفير كلمات المرور** - باستخدام bcrypt
- **حماية CSRF** - لجميع النماذج
- **تسجيل العمليات** - Audit Logs شامل
- **التحقق من الصلاحيات** - على مستوى الطرق والواجهات
- **حماية من SQL Injection** - باستخدام Eloquent ORM

## 📱 التصميم المتجاوب

- **Bootstrap 5** - تصميم حديث ومتجاوب
- **Font Awesome** - أيقونات احترافية
- **Chart.js** - رسوم بيانية تفاعلية
- **دعم RTL** - للغة العربية

## 🔧 التطوير

### إضافة وحدة جديدة
1. إنشاء Migration للجدول
2. إنشاء Model مع العلاقات
3. إنشاء Controller مع الطرق المطلوبة
4. إضافة Routes في web.php
5. إنشاء Views للواجهات
6. إضافة الصلاحيات في Seeder

### إضافة صلاحية جديدة
```php
// في RolesAndPermissionsSeeder.php
['name' => 'module.action', 'display_name_ar' => 'الوصف بالعربية', 'module' => 'module_name']
```

## 📈 التقارير

النظام يوفر تقارير شاملة:
- **تقارير المنظمات** - إحصائيات وأداء
- **تقارير الموظفين** - الحضور والأداء
- **تقارير المتطوعين** - ساعات التطوع والأنشطة
- **تقارير المشاريع** - التقدم والميزانيات
- **تقارير التبرعات** - المبالغ والمتبرعين
- **تقارير مالية** - القوائم المالية

## 🎯 الخطوات التالية

لإكمال النظام، يمكنك:

1. **إضافة المزيد من Controllers** للوحدات المختلفة
2. **إنشاء Views** للواجهات المطلوبة
3. **تطوير API** للتكامل مع أنظمة خارجية
4. **إضافة نظام الإشعارات** الفوري
5. **تطوير تطبيق موبايل** مصاحب
6. **إضافة نظام التقارير** المتقدم
7. **تطوير نظام الدفع الإلكتروني** للتبرعات

## 📞 الدعم

للدعم والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الوثائق: [docs.example.com](https://docs.example.com)

## 🙏 شكر وتقدير

شكر خاص لـ:
- فريق Laravel لإطار العمل الرائع
- مجتمع المطورين العرب
- جميع المساهمين في المشروع

---

**ملاحظة**: هذا النظام جاهز للاستخدام مع البنية الأساسية المكتملة. يمكنك البناء عليه وإضافة المزيد من الميزات حسب احتياجاتك.
