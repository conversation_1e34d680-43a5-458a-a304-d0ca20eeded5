<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Organization;
use App\Models\Role;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // تشغيل seeder للأدوار والصلاحيات
        $this->call([
            RolesAndPermissionsSeeder::class,
        ]);

        // إنشاء منظمة تجريبية
        $organization = Organization::create([
            'name_ar' => 'جمعية الخير الخيرية',
            'name_en' => 'Al-Khair Charity Association',
            'registration_number' => 'REG-2024-001',
            'type' => 'charity',
            'status' => 'active',
            'description_ar' => 'جمعية خيرية تهدف إلى خدمة المجتمع وتقديم المساعدات للمحتاجين',
            'description_en' => 'A charity association aimed at serving the community and providing assistance to those in need',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'website' => 'https://www.alkhair.org',
            'address' => 'شارع الملك فهد، الرياض',
            'city' => 'الرياض',
            'region' => 'منطقة الرياض',
            'postal_code' => '12345',
            'license_number' => 'LIC-2024-001',
            'license_issue_date' => '2024-01-01',
            'license_expiry_date' => '2026-12-31',
            'license_authority' => 'وزارة الموارد البشرية والتنمية الاجتماعية',
            'annual_budget' => 5000000.00,
            'tax_number' => '*********',
            'establishment_date' => '2020-01-01',
            'employees_count' => 25,
            'volunteers_count' => 100,
            'services' => ['التعليم', 'الصحة', 'الإغاثة', 'رعاية الأيتام'],
            'target_groups' => ['الأيتام', 'المسنين', 'ذوي الإعاقة', 'الأسر المحتاجة'],
            'performance_score' => 4.5,
            'governance_level' => 'excellent',
        ]);

        // إنشاء مستخدم مدير النظام
        $superAdmin = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name_ar' => 'مدير',
            'last_name_ar' => 'النظام',
            'first_name_en' => 'System',
            'last_name_en' => 'Administrator',
            'phone' => '+966501234567',
            'status' => 'active',
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'organization_id' => null, // مدير النظام لا ينتمي لمنظمة محددة
        ]);

        // تعيين دور مدير النظام
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->assignRole($superAdminRole);

        // إنشاء مستخدم مدير المنظمة
        $orgAdmin = User::create([
            'name' => 'مدير المنظمة',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name_ar' => 'أحمد',
            'last_name_ar' => 'المحمد',
            'first_name_en' => 'Ahmed',
            'last_name_en' => 'Al-Mohammed',
            'phone' => '+966501234568',
            'status' => 'active',
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'organization_id' => $organization->id,
        ]);

        // تعيين دور مدير المنظمة
        $orgAdminRole = Role::where('name', 'organization_admin')->first();
        $orgAdmin->assignRole($orgAdminRole, $organization->id);

        // إنشاء مستخدم مدير الموارد البشرية
        $hrManager = User::create([
            'name' => 'مدير الموارد البشرية',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name_ar' => 'فاطمة',
            'last_name_ar' => 'العلي',
            'first_name_en' => 'Fatima',
            'last_name_en' => 'Al-Ali',
            'phone' => '+966501234569',
            'status' => 'active',
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'organization_id' => $organization->id,
        ]);

        // تعيين دور مدير الموارد البشرية
        $hrManagerRole = Role::where('name', 'hr_manager')->first();
        $hrManager->assignRole($hrManagerRole, $organization->id);

        // إنشاء مستخدم مدير المشاريع
        $projectManager = User::create([
            'name' => 'مدير المشاريع',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name_ar' => 'محمد',
            'last_name_ar' => 'السعد',
            'first_name_en' => 'Mohammed',
            'last_name_en' => 'Al-Saad',
            'phone' => '+966501234570',
            'status' => 'active',
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'organization_id' => $organization->id,
        ]);

        // تعيين دور مدير المشاريع
        $projectManagerRole = Role::where('name', 'project_manager')->first();
        $projectManager->assignRole($projectManagerRole, $organization->id);

        // إنشاء مستخدم مدير المالية
        $financeManager = User::create([
            'name' => 'مدير المالية',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name_ar' => 'خالد',
            'last_name_ar' => 'الأحمد',
            'first_name_en' => 'Khalid',
            'last_name_en' => 'Al-Ahmad',
            'phone' => '+966501234571',
            'status' => 'active',
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'organization_id' => $organization->id,
        ]);

        // تعيين دور مدير المالية
        $financeManagerRole = Role::where('name', 'finance_manager')->first();
        $financeManager->assignRole($financeManagerRole, $organization->id);

        $this->command->info('تم إنشاء البيانات الأولية بنجاح!');
        $this->command->info('بيانات الدخول:');
        $this->command->info('مدير النظام: <EMAIL> / password');
        $this->command->info('مدير المنظمة: <EMAIL> / password');
        $this->command->info('مدير الموارد البشرية: <EMAIL> / password');
        $this->command->info('مدير المشاريع: <EMAIL> / password');
        $this->command->info('مدير المالية: <EMAIL> / password');
    }
}
