-- إعد<PERSON> قاعدة البيانات لنظام إدارة مؤسسات القطاع
-- Sector Management System Database Setup

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS sector_management_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE sector_management_system;

-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- CREATE USER 'sector_user'@'localhost' IDENTIFIED BY 'secure_password';
-- GRANT ALL PRIVILEGES ON sector_management_system.* TO 'sector_user'@'localhost';
-- FLUSH PRIVILEGES;

-- تعليقات للمطور
-- بعد تشغيل هذا الملف، قم بتشغيل الأوامر التالية:
-- php artisan migrate
-- php artisan db:seed

-- معلومات قاعدة البيانات
SELECT 
    'قاعدة البيانات تم إنشاؤها بنجاح' AS status,
    'sector_management_system' AS database_name,
    'utf8mb4_unicode_ci' AS collation,
    NOW() AS created_at;
