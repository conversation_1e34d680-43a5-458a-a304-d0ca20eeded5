<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('volunteers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            
            // المعلومات الشخصية
            $table->string('volunteer_number')->unique()->comment('رقم المتطوع');
            $table->string('first_name_ar')->comment('الاسم الأول بالعربية');
            $table->string('last_name_ar')->comment('اسم العائلة بالعربية');
            $table->string('first_name_en')->nullable()->comment('الاسم الأول بالإنجليزية');
            $table->string('last_name_en')->nullable()->comment('اسم العائلة بالإنجليزية');
            $table->string('national_id')->unique()->comment('رقم الهوية الوطنية');
            $table->date('birth_date')->comment('تاريخ الميلاد');
            $table->enum('gender', ['male', 'female'])->comment('الجنس');
            $table->enum('marital_status', ['single', 'married', 'divorced', 'widowed'])->comment('الحالة الاجتماعية');
            $table->string('nationality')->comment('الجنسية');
            
            // معلومات الاتصال
            $table->string('email')->unique();
            $table->string('phone')->comment('رقم الهاتف');
            $table->string('emergency_contact_name')->nullable()->comment('اسم جهة الاتصال في الطوارئ');
            $table->string('emergency_contact_phone')->nullable()->comment('رقم هاتف الطوارئ');
            $table->text('address')->nullable()->comment('العنوان');
            $table->string('city')->nullable()->comment('المدينة');
            
            // معلومات التطوع
            $table->date('registration_date')->comment('تاريخ التسجيل');
            $table->enum('status', ['active', 'inactive', 'suspended', 'blacklisted'])->default('active')->comment('حالة المتطوع');
            $table->json('areas_of_interest')->nullable()->comment('مجالات الاهتمام');
            $table->json('skills')->nullable()->comment('المهارات');
            $table->json('languages')->nullable()->comment('اللغات');
            $table->enum('availability', ['weekdays', 'weekends', 'both', 'flexible'])->comment('الوقت المتاح');
            $table->json('available_times')->nullable()->comment('الأوقات المتاحة تفصيلياً');
            
            // المؤهلات والخبرات
            $table->json('qualifications')->nullable()->comment('المؤهلات العلمية');
            $table->json('experiences')->nullable()->comment('الخبرات السابقة في التطوع');
            $table->json('certifications')->nullable()->comment('الشهادات');
            $table->json('training_courses')->nullable()->comment('الدورات التدريبية');
            
            // معلومات الصحة والسلامة
            $table->json('health_conditions')->nullable()->comment('الحالات الصحية');
            $table->boolean('has_criminal_record')->default(false)->comment('وجود سجل جنائي');
            $table->text('criminal_record_details')->nullable()->comment('تفاصيل السجل الجنائي');
            
            // إحصائيات التطوع
            $table->integer('total_volunteer_hours')->default(0)->comment('إجمالي ساعات التطوع');
            $table->integer('completed_activities')->default(0)->comment('الأنشطة المكتملة');
            $table->decimal('rating', 3, 2)->nullable()->comment('تقييم المتطوع');
            $table->integer('no_show_count')->default(0)->comment('عدد مرات عدم الحضور');
            
            // المكافآت والتقدير
            $table->json('awards')->nullable()->comment('الجوائز والتقديرات');
            $table->json('achievements')->nullable()->comment('الإنجازات');
            $table->integer('points')->default(0)->comment('نقاط التطوع');
            
            // التفضيلات
            $table->json('preferences')->nullable()->comment('التفضيلات');
            $table->boolean('newsletter_subscription')->default(true)->comment('الاشتراك في النشرة الإخبارية');
            $table->boolean('sms_notifications')->default(true)->comment('إشعارات الرسائل النصية');
            $table->boolean('email_notifications')->default(true)->comment('إشعارات البريد الإلكتروني');
            
            // الوثائق والملفات
            $table->string('photo')->nullable()->comment('صورة المتطوع');
            $table->json('documents')->nullable()->comment('الوثائق والملفات');
            
            // معلومات إضافية
            $table->text('motivation')->nullable()->comment('دافع التطوع');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->text('feedback')->nullable()->comment('ملاحظات المتطوع');
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['organization_id', 'status']);
            $table->index(['city', 'status']);
            $table->index('registration_date');
            $table->index('rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('volunteers');
    }
};
