<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Organization;
use App\Models\User;
use App\Models\Employee;
use App\Models\Volunteer;
use App\Models\Project;
use App\Models\Donation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $organizationId = $user->organization_id;

        // إحصائيات عامة
        $stats = $this->getGeneralStats($organizationId);
        
        // إحصائيات المشاريع
        $projectStats = $this->getProjectStats($organizationId);
        
        // إحصائيات التبرعات
        $donationStats = $this->getDonationStats($organizationId);
        
        // الأنشطة الأخيرة
        $recentActivities = $this->getRecentActivities($organizationId);
        
        // المشاريع النشطة
        $activeProjects = $this->getActiveProjects($organizationId);
        
        // التبرعات الأخيرة
        $recentDonations = $this->getRecentDonations($organizationId);

        return view('dashboard.index', compact(
            'stats',
            'projectStats',
            'donationStats',
            'recentActivities',
            'activeProjects',
            'recentDonations'
        ));
    }

    private function getGeneralStats($organizationId)
    {
        $query = $organizationId ? 
            fn($q) => $q->where('organization_id', $organizationId) : 
            fn($q) => $q;

        return [
            'total_organizations' => $organizationId ? 1 : Organization::active()->count(),
            'total_employees' => Employee::when($organizationId, $query)->active()->count(),
            'total_volunteers' => Volunteer::when($organizationId, $query)->active()->count(),
            'total_projects' => Project::when($organizationId, $query)->count(),
            'active_projects' => Project::when($organizationId, $query)->active()->count(),
            'completed_projects' => Project::when($organizationId, $query)->completed()->count(),
            'total_donations' => Donation::when($organizationId, $query)->confirmed()->sum('amount'),
            'total_donors' => Donation::when($organizationId, $query)->confirmed()->distinct('donor_id')->count(),
        ];
    }

    private function getProjectStats($organizationId)
    {
        $query = Project::when($organizationId, function($q) use ($organizationId) {
            return $q->where('organization_id', $organizationId);
        });

        return [
            'by_status' => $query->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
            'by_type' => $query->select('type', DB::raw('count(*) as count'))
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
            'budget_utilization' => $query->selectRaw('
                SUM(total_budget) as total_budget,
                SUM(spent_amount) as spent_amount,
                AVG(completion_percentage) as avg_completion
            ')->first(),
        ];
    }

    private function getDonationStats($organizationId)
    {
        $query = Donation::when($organizationId, function($q) use ($organizationId) {
            return $q->where('organization_id', $organizationId);
        })->confirmed();

        // إحصائيات الشهر الحالي
        $currentMonth = $query->whereMonth('donation_date', now()->month)
            ->whereYear('donation_date', now()->year);

        // إحصائيات الشهر الماضي
        $lastMonth = $query->whereMonth('donation_date', now()->subMonth()->month)
            ->whereYear('donation_date', now()->subMonth()->year);

        $currentMonthTotal = $currentMonth->sum('amount');
        $lastMonthTotal = $lastMonth->sum('amount');

        return [
            'current_month' => $currentMonthTotal,
            'last_month' => $lastMonthTotal,
            'growth_percentage' => $lastMonthTotal > 0 ? 
                round((($currentMonthTotal - $lastMonthTotal) / $lastMonthTotal) * 100, 2) : 0,
            'by_type' => $query->select('type', DB::raw('SUM(amount) as total'))
                ->groupBy('type')
                ->pluck('total', 'type')
                ->toArray(),
            'monthly_trend' => $query->selectRaw('
                YEAR(donation_date) as year,
                MONTH(donation_date) as month,
                SUM(amount) as total
            ')
                ->where('donation_date', '>=', now()->subMonths(12))
                ->groupBy('year', 'month')
                ->orderBy('year')
                ->orderBy('month')
                ->get()
                ->map(function($item) {
                    return [
                        'period' => $item->year . '-' . str_pad($item->month, 2, '0', STR_PAD_LEFT),
                        'total' => $item->total
                    ];
                }),
        ];
    }

    private function getRecentActivities($organizationId)
    {
        $activities = collect();

        // أحدث المشاريع
        $recentProjects = Project::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($project) {
                return [
                    'type' => 'project',
                    'title' => 'مشروع جديد: ' . $project->name,
                    'description' => $project->description,
                    'date' => $project->created_at,
                    'icon' => 'fas fa-project-diagram',
                    'color' => 'primary'
                ];
            });

        // أحدث التبرعات
        $recentDonations = Donation::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->with('donor')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($donation) {
                return [
                    'type' => 'donation',
                    'title' => 'تبرع جديد من ' . $donation->donor->name,
                    'description' => 'مبلغ ' . number_format($donation->amount, 2) . ' ' . $donation->currency,
                    'date' => $donation->created_at,
                    'icon' => 'fas fa-hand-holding-heart',
                    'color' => 'success'
                ];
            });

        // أحدث الموظفين
        $recentEmployees = Employee::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->latest()
            ->limit(3)
            ->get()
            ->map(function($employee) {
                return [
                    'type' => 'employee',
                    'title' => 'موظف جديد: ' . $employee->full_name,
                    'description' => $employee->job_title . ' - ' . $employee->department,
                    'date' => $employee->created_at,
                    'icon' => 'fas fa-user-tie',
                    'color' => 'info'
                ];
            });

        return $activities
            ->merge($recentProjects)
            ->merge($recentDonations)
            ->merge($recentEmployees)
            ->sortByDesc('date')
            ->take(10)
            ->values();
    }

    private function getActiveProjects($organizationId)
    {
        return Project::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->active()
            ->with(['manager', 'organization'])
            ->orderBy('priority', 'desc')
            ->orderBy('end_date', 'asc')
            ->limit(5)
            ->get();
    }

    private function getRecentDonations($organizationId)
    {
        return Donation::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->with(['donor', 'project'])
            ->latest()
            ->limit(10)
            ->get();
    }

    public function getChartData(Request $request)
    {
        $type = $request->get('type');
        $organizationId = Auth::user()->organization_id;

        switch ($type) {
            case 'donations_monthly':
                return $this->getDonationsMonthlyChart($organizationId);
            case 'projects_status':
                return $this->getProjectsStatusChart($organizationId);
            case 'volunteers_activities':
                return $this->getVolunteersActivitiesChart($organizationId);
            default:
                return response()->json(['error' => 'Invalid chart type'], 400);
        }
    }

    private function getDonationsMonthlyChart($organizationId)
    {
        $data = Donation::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->confirmed()
            ->selectRaw('
                YEAR(donation_date) as year,
                MONTH(donation_date) as month,
                SUM(amount) as total
            ')
            ->where('donation_date', '>=', now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return response()->json([
            'labels' => $data->map(function($item) {
                return $item->year . '-' . str_pad($item->month, 2, '0', STR_PAD_LEFT);
            }),
            'datasets' => [{
                'label' => 'التبرعات الشهرية',
                'data' => $data->pluck('total'),
                'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                'borderColor' => 'rgba(54, 162, 235, 1)',
                'borderWidth' => 1
            }]
        ]);
    }

    private function getProjectsStatusChart($organizationId)
    {
        $data = Project::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        return response()->json([
            'labels' => $data->pluck('status'),
            'datasets' => [{
                'data' => $data->pluck('count'),
                'backgroundColor' => [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        ]);
    }

    private function getVolunteersActivitiesChart($organizationId)
    {
        // هذا مثال - يمكن تطويره بناءً على جدول الأنشطة
        $data = Volunteer::when($organizationId, function($q) use ($organizationId) {
                return $q->where('organization_id', $organizationId);
            })
            ->selectRaw('
                status,
                COUNT(*) as count,
                SUM(total_volunteer_hours) as total_hours
            ')
            ->groupBy('status')
            ->get();

        return response()->json([
            'labels' => $data->pluck('status'),
            'datasets' => [
                [
                    'label' => 'عدد المتطوعين',
                    'data' => $data->pluck('count'),
                    'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'ساعات التطوع',
                    'data' => $data->pluck('total_hours'),
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1
                ]
            ]
        ]);
    }
}
