<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('manager_id')->nullable()->constrained('employees')->onDelete('set null');
            
            // معلومات المشروع الأساسية
            $table->string('project_code')->unique()->comment('رمز المشروع');
            $table->string('name_ar')->comment('اسم المشروع بالعربية');
            $table->string('name_en')->nullable()->comment('اسم المشروع بالإنجليزية');
            $table->text('description_ar')->comment('وصف المشروع بالعربية');
            $table->text('description_en')->nullable()->comment('وصف المشروع بالإنجليزية');
            $table->enum('type', ['development', 'humanitarian', 'educational', 'health', 'social', 'environmental'])->comment('نوع المشروع');
            $table->enum('status', ['planning', 'active', 'on_hold', 'completed', 'cancelled'])->default('planning')->comment('حالة المشروع');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium')->comment('أولوية المشروع');
            
            // التواريخ
            $table->date('start_date')->comment('تاريخ البداية');
            $table->date('end_date')->comment('تاريخ الانتهاء المخطط');
            $table->date('actual_end_date')->nullable()->comment('تاريخ الانتهاء الفعلي');
            
            // المعلومات المالية
            $table->decimal('total_budget', 15, 2)->comment('الميزانية الإجمالية');
            $table->decimal('spent_amount', 15, 2)->default(0)->comment('المبلغ المصروف');
            $table->decimal('remaining_budget', 15, 2)->comment('الميزانية المتبقية');
            $table->string('funding_source')->nullable()->comment('مصدر التمويل');
            
            // الموقع والنطاق
            $table->json('target_locations')->nullable()->comment('المواقع المستهدفة');
            $table->json('target_beneficiaries')->nullable()->comment('المستفيدين المستهدفين');
            $table->integer('expected_beneficiaries_count')->default(0)->comment('عدد المستفيدين المتوقع');
            $table->integer('actual_beneficiaries_count')->default(0)->comment('عدد المستفيدين الفعلي');
            
            // الأهداف والمؤشرات
            $table->json('objectives')->nullable()->comment('أهداف المشروع');
            $table->json('kpis')->nullable()->comment('مؤشرات الأداء الرئيسية');
            $table->json('milestones')->nullable()->comment('المعالم الرئيسية');
            
            // المخاطر والتحديات
            $table->json('risks')->nullable()->comment('المخاطر المحتملة');
            $table->json('challenges')->nullable()->comment('التحديات');
            $table->json('mitigation_strategies')->nullable()->comment('استراتيجيات التخفيف');
            
            // فريق العمل
            $table->json('team_members')->nullable()->comment('أعضاء فريق العمل');
            $table->json('stakeholders')->nullable()->comment('أصحاب المصلحة');
            
            // التقدم والتقييم
            $table->decimal('completion_percentage', 5, 2)->default(0)->comment('نسبة الإنجاز');
            $table->decimal('performance_score', 3, 2)->nullable()->comment('درجة تقييم الأداء');
            $table->text('lessons_learned')->nullable()->comment('الدروس المستفادة');
            
            // الوثائق والملفات
            $table->json('documents')->nullable()->comment('الوثائق والملفات');
            $table->json('reports')->nullable()->comment('التقارير');
            
            // معلومات إضافية
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->boolean('is_featured')->default(false)->comment('مشروع مميز');
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['organization_id', 'status']);
            $table->index(['type', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
