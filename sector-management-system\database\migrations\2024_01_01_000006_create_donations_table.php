<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول المتبرعين
        Schema::create('donors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            
            // معلومات المتبرع
            $table->string('donor_code')->unique()->comment('رمز المتبرع');
            $table->enum('donor_type', ['individual', 'corporate', 'foundation', 'government'])->comment('نوع المتبرع');
            $table->string('name_ar')->comment('اسم المتبرع بالعربية');
            $table->string('name_en')->nullable()->comment('اسم المتبرع بالإنجليزية');
            $table->string('national_id')->nullable()->comment('رقم الهوية/السجل التجاري');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable()->comment('العنوان');
            $table->string('city')->nullable()->comment('المدينة');
            $table->string('country')->nullable()->comment('البلد');
            
            // معلومات إضافية للمؤسسات
            $table->string('contact_person')->nullable()->comment('الشخص المسؤول');
            $table->string('contact_person_title')->nullable()->comment('منصب الشخص المسؤول');
            $table->string('website')->nullable();
            
            // إحصائيات التبرع
            $table->decimal('total_donations', 15, 2)->default(0)->comment('إجمالي التبرعات');
            $table->integer('donations_count')->default(0)->comment('عدد التبرعات');
            $table->date('first_donation_date')->nullable()->comment('تاريخ أول تبرع');
            $table->date('last_donation_date')->nullable()->comment('تاريخ آخر تبرع');
            
            // التصنيف والتقييم
            $table->enum('category', ['major', 'regular', 'occasional', 'one_time'])->default('one_time')->comment('فئة المتبرع');
            $table->enum('status', ['active', 'inactive', 'blacklisted'])->default('active')->comment('حالة المتبرع');
            $table->decimal('rating', 3, 2)->nullable()->comment('تقييم المتبرع');
            
            // التفضيلات
            $table->json('preferred_causes')->nullable()->comment('القضايا المفضلة');
            $table->boolean('anonymous_donations')->default(false)->comment('التبرع المجهول');
            $table->boolean('newsletter_subscription')->default(true)->comment('الاشتراك في النشرة');
            $table->boolean('tax_receipt_required')->default(true)->comment('يتطلب إيصال ضريبي');
            
            // ملاحظات
            $table->text('notes')->nullable()->comment('ملاحظات');
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['organization_id', 'status']);
            $table->index(['donor_type', 'category']);
            $table->index('total_donations');
        });

        // جدول التبرعات
        Schema::create('donations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('donor_id')->constrained('donors')->onDelete('cascade');
            $table->foreignId('project_id')->nullable()->constrained('projects')->onDelete('set null');
            $table->foreignId('campaign_id')->nullable()->comment('معرف الحملة');
            
            // معلومات التبرع
            $table->string('donation_number')->unique()->comment('رقم التبرع');
            $table->decimal('amount', 15, 2)->comment('مبلغ التبرع');
            $table->string('currency', 3)->default('SAR')->comment('العملة');
            $table->enum('type', ['cash', 'in_kind', 'service', 'zakat', 'sadaqah'])->comment('نوع التبرع');
            $table->enum('frequency', ['one_time', 'monthly', 'quarterly', 'annually'])->default('one_time')->comment('تكرار التبرع');
            $table->date('donation_date')->comment('تاريخ التبرع');
            $table->enum('status', ['pending', 'confirmed', 'cancelled', 'refunded'])->default('pending')->comment('حالة التبرع');
            
            // معلومات الدفع
            $table->enum('payment_method', ['cash', 'bank_transfer', 'credit_card', 'check', 'online'])->comment('طريقة الدفع');
            $table->string('payment_reference')->nullable()->comment('مرجع الدفع');
            $table->date('payment_date')->nullable()->comment('تاريخ الدفع');
            $table->string('receipt_number')->nullable()->comment('رقم الإيصال');
            
            // التخصيص والغرض
            $table->string('purpose_ar')->comment('الغرض من التبرع بالعربية');
            $table->string('purpose_en')->nullable()->comment('الغرض من التبرع بالإنجليزية');
            $table->json('allocation')->nullable()->comment('توزيع التبرع على الأنشطة');
            $table->boolean('is_zakat')->default(false)->comment('هل هو زكاة');
            $table->boolean('is_anonymous')->default(false)->comment('تبرع مجهول');
            
            // معلومات التبرع العينية
            $table->text('in_kind_description')->nullable()->comment('وصف التبرع العينية');
            $table->decimal('estimated_value', 15, 2)->nullable()->comment('القيمة المقدرة للتبرع العيني');
            
            // المعالجة والموافقة
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('processed_at')->nullable()->comment('وقت المعالجة');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable()->comment('وقت الموافقة');
            
            // الوثائق والملفات
            $table->json('documents')->nullable()->comment('الوثائق المرفقة');
            $table->string('receipt_file')->nullable()->comment('ملف الإيصال');
            
            // ملاحظات
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->text('donor_message')->nullable()->comment('رسالة المتبرع');
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['organization_id', 'status']);
            $table->index(['donor_id', 'donation_date']);
            $table->index(['project_id', 'status']);
            $table->index(['type', 'status']);
            $table->index('donation_date');
            $table->index('amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('donations');
        Schema::dropIfExists('donors');
    }
};
