<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: simple_login.php');
    exit;
}

// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: simple_login.php');
    exit;
}

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=sector_management_system;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // جلب بيانات المستخدم
    $stmt = $pdo->prepare("SELECT u.*, o.name_ar as organization_name FROM users u LEFT JOIN organizations o ON u.organization_id = o.id WHERE u.id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب المنظمات
    $search = $_GET['search'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    $type_filter = $_GET['type'] ?? '';
    
    $sql = "SELECT * FROM organizations WHERE 1=1";
    $params = [];
    
    if ($search) {
        $sql .= " AND (name_ar LIKE ? OR name_en LIKE ? OR registration_number LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($status_filter) {
        $sql .= " AND status = ?";
        $params[] = $status_filter;
    }
    
    if ($type_filter) {
        $sql .= " AND type = ?";
        $params[] = $type_filter;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $organizations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات المنظمات
    $stats = [];
    $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM organizations GROUP BY status");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $stats[$row['status']] = $row['count'];
    }
    
} catch (PDOException $e) {
    $error = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
}

// ترجمة الحالات والأنواع
$status_labels = [
    'active' => 'نشطة',
    'inactive' => 'غير نشطة',
    'suspended' => 'معلقة',
    'pending' => 'في الانتظار'
];

$type_labels = [
    'charity' => 'جمعية خيرية',
    'ngo' => 'منظمة غير حكومية',
    'foundation' => 'مؤسسة',
    'association' => 'جمعية'
];

$status_colors = [
    'active' => 'success',
    'inactive' => 'secondary',
    'suspended' => 'warning',
    'pending' => 'info'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنظمات - نظام إدارة مؤسسات القطاع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .main-content {
            margin-right: 250px;
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top">
        <div class="container-fluid">
            <button class="btn btn-link d-lg-none" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-building text-primary"></i>
                نظام إدارة مؤسسات القطاع
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i>
                        <?= htmlspecialchars($user['name']) ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-user"></i> الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="?logout=1">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="d-flex">
        <!-- Sidebar -->
        <nav class="sidebar position-fixed" id="sidebar">
            <div class="p-3">
                <div class="text-center mb-4">
                    <img src="https://via.placeholder.com/80x80" alt="Logo" class="rounded-circle mb-2">
                    <h6><?= htmlspecialchars($user['organization_name'] ?? 'النظام') ?></h6>
                </div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link active" href="organizations.php">
                            <i class="fas fa-building me-2"></i>
                            المنظمات
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="employees.php">
                            <i class="fas fa-users me-2"></i>
                            الموظفين
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="volunteers.php">
                            <i class="fas fa-hands-helping me-2"></i>
                            المتطوعين
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="projects.php">
                            <i class="fas fa-project-diagram me-2"></i>
                            المشاريع
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-hand-holding-heart me-2"></i>
                            التبرعات
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" style="margin-top: 76px;">
            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i>
                    <?= htmlspecialchars($error) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">إدارة المنظمات</h1>
                    <p class="text-muted">إدارة وتتبع المنظمات والجمعيات المسجلة</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOrganizationModal">
                        <i class="fas fa-plus"></i> إضافة منظمة جديدة
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h4 mb-0"><?= $stats['active'] ?? 0 ?></div>
                                <div>منظمات نشطة</div>
                            </div>
                            <div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h4 mb-0"><?= $stats['pending'] ?? 0 ?></div>
                                <div>في الانتظار</div>
                            </div>
                            <div>
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h4 mb-0"><?= $stats['suspended'] ?? 0 ?></div>
                                <div>معلقة</div>
                            </div>
                            <div>
                                <i class="fas fa-pause-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="h4 mb-0"><?= array_sum($stats) ?></div>
                                <div>إجمالي المنظمات</div>
                            </div>
                            <div>
                                <i class="fas fa-building fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="اسم المنظمة أو رقم التسجيل">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <?php foreach ($status_labels as $value => $label): ?>
                                    <option value="<?= $value ?>" <?= $status_filter === $value ? 'selected' : '' ?>>
                                        <?= $label ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">النوع</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">جميع الأنواع</option>
                                <?php foreach ($type_labels as $value => $label): ?>
                                    <option value="<?= $value ?>" <?= $type_filter === $value ? 'selected' : '' ?>>
                                        <?= $label ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Organizations Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building"></i>
                        قائمة المنظمات (<?= count($organizations) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($organizations)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منظمات</h5>
                            <p class="text-muted">لم يتم العثور على منظمات تطابق معايير البحث</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المنظمة</th>
                                        <th>رقم التسجيل</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>المدينة</th>
                                        <th>عدد الموظفين</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($organizations as $org): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                        <i class="fas fa-building"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?= htmlspecialchars($org['name_ar']) ?></div>
                                                        <?php if ($org['name_en']): ?>
                                                            <small class="text-muted"><?= htmlspecialchars($org['name_en']) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code><?= htmlspecialchars($org['registration_number']) ?></code>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $type_labels[$org['type']] ?? $org['type'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $status_colors[$org['status']] ?? 'secondary' ?>">
                                                    <?= $status_labels[$org['status']] ?? $org['status'] ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($org['city'] ?? '-') ?></td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= number_format($org['employees_count']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('Y/m/d', strtotime($org['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sidebar Toggle
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('show');
        });
    </script>
</body>
</html>
