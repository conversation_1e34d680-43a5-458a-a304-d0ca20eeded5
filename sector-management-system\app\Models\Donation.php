<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Donation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'donor_id',
        'project_id',
        'campaign_id',
        'donation_number',
        'amount',
        'currency',
        'type',
        'frequency',
        'donation_date',
        'status',
        'payment_method',
        'payment_reference',
        'payment_date',
        'receipt_number',
        'purpose_ar',
        'purpose_en',
        'allocation',
        'is_zakat',
        'is_anonymous',
        'in_kind_description',
        'estimated_value',
        'processed_by',
        'processed_at',
        'approved_by',
        'approved_at',
        'documents',
        'receipt_file',
        'notes',
        'donor_message',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'estimated_value' => 'decimal:2',
        'donation_date' => 'date',
        'payment_date' => 'date',
        'processed_at' => 'datetime',
        'approved_at' => 'datetime',
        'allocation' => 'array',
        'documents' => 'array',
        'is_zakat' => 'boolean',
        'is_anonymous' => 'boolean',
    ];

    // العلاقات
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function donor()
    {
        return $this->belongsTo(Donor::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function processedBy()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // الخصائص المحسوبة
    public function getPurposeAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->purpose_ar : ($this->purpose_en ?? $this->purpose_ar);
    }

    public function getIsConfirmedAttribute()
    {
        return $this->status === 'confirmed';
    }

    public function getIsPendingAttribute()
    {
        return $this->status === 'pending';
    }

    public function getIsCashAttribute()
    {
        return $this->type === 'cash';
    }

    public function getIsInKindAttribute()
    {
        return $this->type === 'in_kind';
    }

    public function getIsRecurringAttribute()
    {
        return $this->frequency !== 'one_time';
    }

    public function getDisplayAmountAttribute()
    {
        if ($this->is_in_kind && $this->estimated_value) {
            return $this->estimated_value;
        }
        return $this->amount;
    }

    public function getDaysToProcessAttribute()
    {
        return $this->processed_at ? $this->created_at->diffInDays($this->processed_at) : null;
    }

    public function getDaysToApproveAttribute()
    {
        return $this->approved_at ? $this->created_at->diffInDays($this->approved_at) : null;
    }

    // النطاقات
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCash($query)
    {
        return $query->where('type', 'cash');
    }

    public function scopeInKind($query)
    {
        return $query->where('type', 'in_kind');
    }

    public function scopeZakat($query)
    {
        return $query->where('is_zakat', true);
    }

    public function scopeAnonymous($query)
    {
        return $query->where('is_anonymous', true);
    }

    public function scopeRecurring($query)
    {
        return $query->where('frequency', '!=', 'one_time');
    }

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeByDonor($query, $donorId)
    {
        return $query->where('donor_id', $donorId);
    }

    public function scopeByProject($query, $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('donation_date', [$startDate, $endDate]);
    }

    public function scopeByAmount($query, $minAmount = null, $maxAmount = null)
    {
        return $query->when($minAmount, function ($q) use ($minAmount) {
                return $q->where('amount', '>=', $minAmount);
            })
            ->when($maxAmount, function ($q) use ($maxAmount) {
                return $q->where('amount', '<=', $maxAmount);
            });
    }

    // الطرق المساعدة
    public function process($userId = null)
    {
        $this->status = 'confirmed';
        $this->processed_by = $userId ?? auth()->id();
        $this->processed_at = now();
        $this->save();

        // تحديث إحصائيات المتبرع
        $this->donor->updateDonationStats();

        return $this;
    }

    public function approve($userId = null)
    {
        $this->approved_by = $userId ?? auth()->id();
        $this->approved_at = now();
        $this->save();

        return $this;
    }

    public function cancel($reason = null)
    {
        $this->status = 'cancelled';
        if ($reason) {
            $this->notes = ($this->notes ? $this->notes . "\n" : '') . "سبب الإلغاء: " . $reason;
        }
        $this->save();

        // تحديث إحصائيات المتبرع
        $this->donor->updateDonationStats();

        return $this;
    }

    public function refund($reason = null)
    {
        $this->status = 'refunded';
        if ($reason) {
            $this->notes = ($this->notes ? $this->notes . "\n" : '') . "سبب الاسترداد: " . $reason;
        }
        $this->save();

        // تحديث إحصائيات المتبرع
        $this->donor->updateDonationStats();

        return $this;
    }

    public function generateReceiptNumber()
    {
        if (!$this->receipt_number) {
            $year = $this->donation_date->year;
            $lastReceipt = static::where('organization_id', $this->organization_id)
                ->whereYear('donation_date', $year)
                ->whereNotNull('receipt_number')
                ->orderBy('receipt_number', 'desc')
                ->first();

            $nextNumber = 1;
            if ($lastReceipt && preg_match('/(\d+)$/', $lastReceipt->receipt_number, $matches)) {
                $nextNumber = intval($matches[1]) + 1;
            }

            $this->receipt_number = sprintf('REC-%d-%06d', $year, $nextNumber);
            $this->save();
        }

        return $this->receipt_number;
    }

    public function getDonationTypes()
    {
        return [
            'cash' => 'نقدي',
            'in_kind' => 'عيني',
            'service' => 'خدمي',
            'zakat' => 'زكاة',
            'sadaqah' => 'صدقة',
        ];
    }

    public function getFrequencyOptions()
    {
        return [
            'one_time' => 'مرة واحدة',
            'monthly' => 'شهري',
            'quarterly' => 'ربع سنوي',
            'annually' => 'سنوي',
        ];
    }

    public function getPaymentMethods()
    {
        return [
            'cash' => 'نقداً',
            'bank_transfer' => 'تحويل بنكي',
            'credit_card' => 'بطاقة ائتمان',
            'check' => 'شيك',
            'online' => 'دفع إلكتروني',
        ];
    }

    public function getDonationStatuses()
    {
        return [
            'pending' => 'في الانتظار',
            'confirmed' => 'مؤكد',
            'cancelled' => 'ملغي',
            'refunded' => 'مسترد',
        ];
    }

    public static function generateDonationNumber($organizationId)
    {
        $year = now()->year;
        $lastDonation = static::where('organization_id', $organizationId)
            ->whereYear('created_at', $year)
            ->orderBy('donation_number', 'desc')
            ->first();

        $nextNumber = 1;
        if ($lastDonation && preg_match('/(\d+)$/', $lastDonation->donation_number, $matches)) {
            $nextNumber = intval($matches[1]) + 1;
        }

        return sprintf('DON-%d-%06d', $year, $nextNumber);
    }

    public function calculateTaxDeduction()
    {
        // حساب الخصم الضريبي بناءً على نوع التبرع والقوانين المحلية
        if ($this->is_zakat || !$this->is_confirmed) {
            return 0;
        }

        // مثال: خصم ضريبي بنسبة 10% من قيمة التبرع
        return $this->amount * 0.10;
    }
}
