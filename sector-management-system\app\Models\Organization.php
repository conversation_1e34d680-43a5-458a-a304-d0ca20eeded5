<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Organization extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name_ar',
        'name_en',
        'registration_number',
        'type',
        'status',
        'description_ar',
        'description_en',
        'email',
        'phone',
        'website',
        'address',
        'city',
        'region',
        'postal_code',
        'license_number',
        'license_issue_date',
        'license_expiry_date',
        'license_authority',
        'annual_budget',
        'tax_number',
        'establishment_date',
        'employees_count',
        'volunteers_count',
        'services',
        'target_groups',
        'performance_score',
        'governance_level',
        'logo',
        'documents',
    ];

    protected $casts = [
        'services' => 'array',
        'target_groups' => 'array',
        'documents' => 'array',
        'license_issue_date' => 'date',
        'license_expiry_date' => 'date',
        'establishment_date' => 'date',
        'annual_budget' => 'decimal:2',
        'performance_score' => 'decimal:2',
    ];

    // العلاقات
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function employees()
    {
        return $this->hasMany(Employee::class);
    }

    public function volunteers()
    {
        return $this->hasMany(Volunteer::class);
    }

    public function projects()
    {
        return $this->hasMany(Project::class);
    }

    public function donors()
    {
        return $this->hasMany(Donor::class);
    }

    public function donations()
    {
        return $this->hasMany(Donation::class);
    }

    // الخصائص المحسوبة
    public function getDisplayNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    public function getDescriptionAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : ($this->description_en ?? $this->description_ar);
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    public function getIsLicenseValidAttribute()
    {
        return $this->license_expiry_date && $this->license_expiry_date->isFuture();
    }

    // النطاقات (Scopes)
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByRegion($query, $region)
    {
        return $query->where('region', $region);
    }

    public function scopeWithValidLicense($query)
    {
        return $query->where('license_expiry_date', '>', now());
    }

    // الطرق المساعدة
    public function updateEmployeesCount()
    {
        $this->employees_count = $this->employees()->where('status', 'active')->count();
        $this->save();
    }

    public function updateVolunteersCount()
    {
        $this->volunteers_count = $this->volunteers()->where('status', 'active')->count();
        $this->save();
    }

    public function getTotalDonationsAmount()
    {
        return $this->donations()->where('status', 'confirmed')->sum('amount');
    }

    public function getActiveProjectsCount()
    {
        return $this->projects()->where('status', 'active')->count();
    }

    public function getCompletedProjectsCount()
    {
        return $this->projects()->where('status', 'completed')->count();
    }
}
