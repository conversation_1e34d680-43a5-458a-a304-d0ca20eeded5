@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">لوحة التحكم</h1>
            <p class="text-muted">مرحباً بك في نظام إدارة مؤسسات القطاع</p>
        </div>
        <div>
            <span class="text-muted">{{ now()->format('Y/m/d - H:i') }}</span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ number_format($stats['total_employees'] ?? 0) }}</div>
                        <div class="stats-label">إجمالي الموظفين</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ number_format($stats['total_volunteers'] ?? 0) }}</div>
                        <div class="stats-label">إجمالي المتطوعين</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-hands-helping fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ number_format($stats['active_projects'] ?? 0) }}</div>
                        <div class="stats-label">المشاريع النشطة</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-project-diagram fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ number_format($stats['total_donations'] ?? 0, 0) }}</div>
                        <div class="stats-label">إجمالي التبرعات (ريال)</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-hand-holding-heart fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i>
                        التبرعات الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="donationsChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i>
                        حالة المشاريع
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="projectsChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock"></i>
                        الأنشطة الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    @if(isset($recentActivities) && $recentActivities->count() > 0)
                        <div class="timeline">
                            @foreach($recentActivities->take(5) as $activity)
                                <div class="timeline-item mb-3">
                                    <div class="d-flex">
                                        <div class="timeline-icon me-3">
                                            <i class="{{ $activity['icon'] ?? 'fas fa-circle' }} text-{{ $activity['color'] ?? 'primary' }}"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <h6 class="mb-1">{{ $activity['title'] }}</h6>
                                            <p class="text-muted mb-1">{{ $activity['description'] }}</p>
                                            <small class="text-muted">{{ $activity['date']->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد أنشطة حديثة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Active Projects -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tasks"></i>
                        المشاريع النشطة
                    </h5>
                </div>
                <div class="card-body">
                    @if(isset($activeProjects) && $activeProjects->count() > 0)
                        @foreach($activeProjects->take(5) as $project)
                            <div class="project-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">{{ $project->name }}</h6>
                                    <span class="badge bg-{{ $project->priority === 'high' ? 'danger' : ($project->priority === 'medium' ? 'warning' : 'info') }}">
                                        {{ $project->priority }}
                                    </span>
                                </div>
                                <p class="text-muted small mb-2">{{ Str::limit($project->description, 100) }}</p>
                                <div class="progress mb-2" style="height: 6px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ $project->completion_percentage }}%"
                                         aria-valuenow="{{ $project->completion_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ $project->completion_percentage }}% مكتمل</small>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i>
                                        {{ $project->end_date->format('Y/m/d') }}
                                    </small>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مشاريع نشطة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Donations -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-hand-holding-heart"></i>
                        التبرعات الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    @if(isset($recentDonations) && $recentDonations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المتبرع</th>
                                        <th>المبلغ</th>
                                        <th>النوع</th>
                                        <th>المشروع</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentDonations->take(10) as $donation)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ $donation->donor->name }}</div>
                                                        <small class="text-muted">{{ $donation->donor->donor_type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">
                                                    {{ number_format($donation->amount, 2) }} {{ $donation->currency }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $donation->type }}</span>
                                            </td>
                                            <td>{{ $donation->project->name ?? 'عام' }}</td>
                                            <td>{{ $donation->donation_date->format('Y/m/d') }}</td>
                                            <td>
                                                <span class="badge bg-{{ $donation->status === 'confirmed' ? 'success' : 'warning' }}">
                                                    {{ $donation->status }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-hand-holding-heart fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد تبرعات حديثة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Donations Chart
    const donationsCtx = document.getElementById('donationsChart').getContext('2d');
    const donationsChart = new Chart(donationsCtx, {
        type: 'line',
        data: {
            labels: @json($donationStats['monthly_trend']->pluck('period') ?? []),
            datasets: [{
                label: 'التبرعات الشهرية',
                data: @json($donationStats['monthly_trend']->pluck('total') ?? []),
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('ar-SA').format(value);
                        }
                    }
                }
            }
        }
    });

    // Projects Status Chart
    const projectsCtx = document.getElementById('projectsChart').getContext('2d');
    const projectsChart = new Chart(projectsCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(@json($projectStats['by_status'] ?? {})),
            datasets: [{
                data: Object.values(@json($projectStats['by_status'] ?? {})),
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endpush
