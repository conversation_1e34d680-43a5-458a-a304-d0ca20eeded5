# دليل النشر - نظام إدارة مؤسسات القطاع

## 🚀 النشر على الخادم المحلي (WAMP/XAMPP)

### الخطوة 1: إعداد البيئة
```bash
# تأكد من تشغيل Apache و MySQL
# نسخ المشروع إلى مجلد www
cp -r sector-management-system C:\wamp64\www\
```

### الخطوة 2: إعداد قاعدة البيانات
```sql
-- في phpMyAdmin أو MySQL Command Line
CREATE DATABASE sector_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### الخطوة 3: تكوين Laravel
```bash
# نسخ ملف البيئة
cp .env.example .env

# تحديث إعدادات قاعدة البيانات في .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sector_management_system
DB_USERNAME=root
DB_PASSWORD=

# إنشاء مفتاح التطبيق
php artisan key:generate

# تشغيل Migrations
php artisan migrate

# تشغيل Seeders
php artisan db:seed
```

### الخطوة 4: إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة للمجلدات
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

## 🌐 النشر على خادم الإنتاج

### متطلبات الخادم
- PHP 8.2+
- MySQL 8.0+
- Apache/Nginx
- Composer
- SSL Certificate (مستحسن)

### خطوات النشر

#### 1. رفع الملفات
```bash
# رفع جميع ملفات المشروع عدا:
# - .env (سيتم إنشاؤه على الخادم)
# - vendor/ (سيتم تثبيته على الخادم)
# - node_modules/
# - storage/logs/*
```

#### 2. تثبيت التبعيات
```bash
composer install --optimize-autoloader --no-dev
```

#### 3. إعداد البيئة
```bash
# إنشاء ملف .env
cp .env.example .env

# تحديث الإعدادات للإنتاج
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# إعدادات قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

# إعدادات البريد الإلكتروني
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
```

#### 4. إعداد قاعدة البيانات
```bash
php artisan key:generate
php artisan migrate --force
php artisan db:seed --force
```

#### 5. تحسين الأداء
```bash
# تحسين التطبيق للإنتاج
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
```

#### 6. إعداد الخادم

##### Apache (.htaccess)
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
```

##### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/your/project/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## 🔒 الأمان في الإنتاج

### 1. إعدادات PHP
```ini
# في php.ini
expose_php = Off
display_errors = Off
log_errors = On
error_log = /path/to/error.log
```

### 2. إعدادات Laravel
```env
# في .env
APP_DEBUG=false
APP_ENV=production
```

### 3. حماية الملفات الحساسة
```apache
# في .htaccess الجذر
<Files .env>
    Order allow,deny
    Deny from all
</Files>

<Files composer.json>
    Order allow,deny
    Deny from all
</Files>
```

### 4. SSL Certificate
```bash
# استخدام Let's Encrypt
certbot --apache -d yourdomain.com
```

## 📊 المراقبة والصيانة

### 1. مراقبة السجلات
```bash
# مراقبة سجلات Laravel
tail -f storage/logs/laravel.log

# مراقبة سجلات الخادم
tail -f /var/log/apache2/error.log
```

### 2. النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf backup_files_$(date +%Y%m%d).tar.gz /path/to/project
```

### 3. التحديثات
```bash
# تحديث التبعيات
composer update

# تشغيل migrations جديدة
php artisan migrate

# إعادة تحسين
php artisan optimize:clear
php artisan optimize
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ 500 Internal Server Error
```bash
# تحقق من السجلات
tail -f storage/logs/laravel.log

# تأكد من الصلاحيات
chmod -R 775 storage bootstrap/cache

# تأكد من ملف .env
php artisan config:clear
```

#### 2. خطأ في قاعدة البيانات
```bash
# تحقق من الاتصال
php artisan tinker
DB::connection()->getPdo();

# تحقق من إعدادات .env
```

#### 3. مشاكل الصلاحيات
```bash
# إعادة تعيين صلاحيات الملفات
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod -R 775 storage bootstrap/cache
```

## 📈 تحسين الأداء

### 1. تحسين قاعدة البيانات
```sql
-- إضافة فهارس للجداول الكبيرة
CREATE INDEX idx_organization_id ON employees(organization_id);
CREATE INDEX idx_donation_date ON donations(donation_date);
```

### 2. تحسين Laravel
```bash
# استخدام Redis للتخزين المؤقت
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

### 3. تحسين الخادم
```bash
# تفعيل ضغط gzip في Apache
a2enmod deflate

# تحسين MySQL
# في my.cnf
innodb_buffer_pool_size = 1G
query_cache_size = 256M
```

---

**ملاحظة**: تأكد من اختبار جميع الوظائف بعد النشر والاحتفاظ بنسخ احتياطية منتظمة.
