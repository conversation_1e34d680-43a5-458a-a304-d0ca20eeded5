<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\Permission;
use App\Models\User;

class PermissionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        try {
            // تسجيل جميع الصلاحيات كـ Gates
            Permission::get()->map(function ($permission) {
                Gate::define($permission->name, function (User $user) use ($permission) {
                    return $user->hasPermission($permission->name, $user->organization_id);
                });
            });
        } catch (\Exception $e) {
            // في حالة عدم وجود جدول الصلاحيات بعد (أثناء التثبيت)
            info('Permission table not found: ' . $e->getMessage());
        }

        // تعريف Gates إضافية
        Gate::define('super-admin', function (User $user) {
            return $user->hasRole('super_admin');
        });

        Gate::define('organization-admin', function (User $user) {
            return $user->hasRole('organization_admin', $user->organization_id);
        });

        Gate::define('same-organization', function (User $user, $model) {
            if (!$user->organization_id) {
                return true; // مدير النظام يمكنه الوصول لكل شيء
            }

            if (method_exists($model, 'getOrganizationId')) {
                return $user->organization_id === $model->getOrganizationId();
            }

            if (isset($model->organization_id)) {
                return $user->organization_id === $model->organization_id;
            }

            return false;
        });

        Gate::define('manage-user', function (User $user, User $targetUser) {
            // مدير النظام يمكنه إدارة أي مستخدم
            if ($user->hasRole('super_admin')) {
                return true;
            }

            // مدير المنظمة يمكنه إدارة مستخدمي منظمته فقط
            if ($user->hasRole('organization_admin', $user->organization_id)) {
                return $user->organization_id === $targetUser->organization_id;
            }

            return false;
        });

        Gate::define('view-organization', function (User $user, $organization) {
            // مدير النظام يمكنه رؤية جميع المنظمات
            if ($user->hasRole('super_admin')) {
                return true;
            }

            // المستخدمون الآخرون يمكنهم رؤية منظمتهم فقط
            return $user->organization_id === $organization->id;
        });

        Gate::define('edit-organization', function (User $user, $organization) {
            // مدير النظام يمكنه تعديل جميع المنظمات
            if ($user->hasRole('super_admin')) {
                return true;
            }

            // مدير المنظمة يمكنه تعديل منظمته فقط
            if ($user->hasRole('organization_admin', $user->organization_id)) {
                return $user->organization_id === $organization->id;
            }

            return false;
        });

        Gate::define('manage-employee', function (User $user, $employee = null) {
            if (!$user->hasPermission('employees.view', $user->organization_id)) {
                return false;
            }

            if (!$employee) {
                return true; // للعرض العام
            }

            // التحقق من نفس المنظمة
            return $user->organization_id === $employee->organization_id;
        });

        Gate::define('manage-volunteer', function (User $user, $volunteer = null) {
            if (!$user->hasPermission('volunteers.view', $user->organization_id)) {
                return false;
            }

            if (!$volunteer) {
                return true; // للعرض العام
            }

            // التحقق من نفس المنظمة
            return $user->organization_id === $volunteer->organization_id;
        });

        Gate::define('manage-project', function (User $user, $project = null) {
            if (!$user->hasPermission('projects.view', $user->organization_id)) {
                return false;
            }

            if (!$project) {
                return true; // للعرض العام
            }

            // التحقق من نفس المنظمة
            return $user->organization_id === $project->organization_id;
        });

        Gate::define('manage-donation', function (User $user, $donation = null) {
            if (!$user->hasPermission('donations.view', $user->organization_id)) {
                return false;
            }

            if (!$donation) {
                return true; // للعرض العام
            }

            // التحقق من نفس المنظمة
            return $user->organization_id === $donation->organization_id;
        });

        Gate::define('view-financial-data', function (User $user) {
            return $user->hasPermission('finance.view', $user->organization_id) ||
                   $user->hasPermission('finance.view_reports', $user->organization_id);
        });

        Gate::define('approve-donations', function (User $user) {
            return $user->hasPermission('donations.approve', $user->organization_id);
        });

        Gate::define('manage-system-settings', function (User $user) {
            return $user->hasRole('super_admin') || 
                   $user->hasPermission('settings.system', $user->organization_id);
        });
    }
}
