@echo off
chcp 65001 >nul
echo ========================================
echo    نظام إدارة مؤسسات القطاع
echo    Sector Management System Setup
echo ========================================
echo.

echo [1/5] التحقق من PHP...
C:\wamp64\bin\php\php8.3.6\php.exe --version
if %errorlevel% neq 0 (
    echo خطأ: PHP غير موجود في المسار المحدد
    echo يرجى التأكد من تثبيت WAMP وتحديث المسار في هذا الملف
    pause
    exit /b 1
)

echo.
echo [2/5] إنشاء قاعدة البيانات...
echo يرجى التأكد من تشغيل MySQL في WAMP
C:\wamp64\bin\mysql\mysql8.3.0\bin\mysql.exe -u root -e "CREATE DATABASE IF NOT EXISTS sector_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo تحذير: قد تكون قاعدة البيانات موجودة مسبقاً أو هناك مشكلة في الاتصال
)

echo.
echo [3/5] تشغيل Migrations...
C:\wamp64\bin\php\php8.3.6\php.exe artisan migrate --force
if %errorlevel% neq 0 (
    echo خطأ في تشغيل Migrations
    echo يرجى التحقق من إعدادات قاعدة البيانات في ملف .env
    pause
    exit /b 1
)

echo.
echo [4/5] تشغيل Seeders...
C:\wamp64\bin\php\php8.3.6\php.exe artisan db:seed --force
if %errorlevel% neq 0 (
    echo خطأ في تشغيل Seeders
    pause
    exit /b 1
)

echo.
echo [5/5] تشغيل الخادم...
echo ========================================
echo تم إعداد النظام بنجاح!
echo.
echo الحسابات التجريبية:
echo - مدير النظام: <EMAIL> / password
echo - مدير المنظمة: <EMAIL> / password
echo - مدير الموارد البشرية: <EMAIL> / password
echo - مدير المشاريع: <EMAIL> / password
echo - مدير المالية: <EMAIL> / password
echo.
echo سيتم تشغيل الخادم على: http://localhost:8000
echo اضغط Ctrl+C لإيقاف الخادم
echo ========================================
echo.

C:\wamp64\bin\php\php8.3.6\php.exe artisan serve

pause
