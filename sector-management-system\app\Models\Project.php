<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Project extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'manager_id',
        'project_code',
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'type',
        'status',
        'priority',
        'start_date',
        'end_date',
        'actual_end_date',
        'total_budget',
        'spent_amount',
        'remaining_budget',
        'funding_source',
        'target_locations',
        'target_beneficiaries',
        'expected_beneficiaries_count',
        'actual_beneficiaries_count',
        'objectives',
        'kpis',
        'milestones',
        'risks',
        'challenges',
        'mitigation_strategies',
        'team_members',
        'stakeholders',
        'completion_percentage',
        'performance_score',
        'lessons_learned',
        'documents',
        'reports',
        'notes',
        'is_featured',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'actual_end_date' => 'date',
        'total_budget' => 'decimal:2',
        'spent_amount' => 'decimal:2',
        'remaining_budget' => 'decimal:2',
        'completion_percentage' => 'decimal:2',
        'performance_score' => 'decimal:2',
        'target_locations' => 'array',
        'target_beneficiaries' => 'array',
        'objectives' => 'array',
        'kpis' => 'array',
        'milestones' => 'array',
        'risks' => 'array',
        'challenges' => 'array',
        'mitigation_strategies' => 'array',
        'team_members' => 'array',
        'stakeholders' => 'array',
        'documents' => 'array',
        'reports' => 'array',
        'is_featured' => 'boolean',
    ];

    // العلاقات
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function manager()
    {
        return $this->belongsTo(Employee::class, 'manager_id');
    }

    public function donations()
    {
        return $this->hasMany(Donation::class);
    }

    // الخصائص المحسوبة
    public function getNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : ($this->name_en ?? $this->name_ar);
    }

    public function getDescriptionAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : ($this->description_en ?? $this->description_ar);
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    public function getIsCompletedAttribute()
    {
        return $this->status === 'completed';
    }

    public function getIsOverdueAttribute()
    {
        return $this->end_date && $this->end_date->isPast() && !$this->is_completed;
    }

    public function getDurationInDaysAttribute()
    {
        return $this->start_date && $this->end_date ? $this->start_date->diffInDays($this->end_date) : 0;
    }

    public function getRemainingDaysAttribute()
    {
        return $this->end_date ? max(0, now()->diffInDays($this->end_date, false)) : 0;
    }

    public function getBudgetUtilizationPercentageAttribute()
    {
        return $this->total_budget > 0 ? round(($this->spent_amount / $this->total_budget) * 100, 2) : 0;
    }

    public function getBeneficiariesReachPercentageAttribute()
    {
        return $this->expected_beneficiaries_count > 0 ? 
            round(($this->actual_beneficiaries_count / $this->expected_beneficiaries_count) * 100, 2) : 0;
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('end_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeWithinBudget($query, $minBudget = null, $maxBudget = null)
    {
        return $query->when($minBudget, function ($q) use ($minBudget) {
                return $q->where('total_budget', '>=', $minBudget);
            })
            ->when($maxBudget, function ($q) use ($maxBudget) {
                return $q->where('total_budget', '<=', $maxBudget);
            });
    }

    // الطرق المساعدة
    public function updateRemainingBudget()
    {
        $this->remaining_budget = $this->total_budget - $this->spent_amount;
        $this->save();
    }

    public function addExpense($amount, $description = null)
    {
        $this->spent_amount += $amount;
        $this->updateRemainingBudget();
        
        // يمكن إضافة سجل تفصيلي للمصروفات هنا
    }

    public function updateProgress($percentage)
    {
        $this->completion_percentage = min(100, max(0, $percentage));
        
        if ($this->completion_percentage >= 100 && $this->status !== 'completed') {
            $this->status = 'completed';
            $this->actual_end_date = now();
        }
        
        $this->save();
    }

    public function addBeneficiaries($count)
    {
        $this->actual_beneficiaries_count += $count;
        $this->save();
    }

    public function getProjectTypes()
    {
        return [
            'development' => 'تنموي',
            'humanitarian' => 'إنساني',
            'educational' => 'تعليمي',
            'health' => 'صحي',
            'social' => 'اجتماعي',
            'environmental' => 'بيئي',
        ];
    }

    public function getProjectStatuses()
    {
        return [
            'planning' => 'قيد التخطيط',
            'active' => 'نشط',
            'on_hold' => 'متوقف مؤقتاً',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
        ];
    }

    public function getPriorities()
    {
        return [
            'low' => 'منخفضة',
            'medium' => 'متوسطة',
            'high' => 'عالية',
            'urgent' => 'عاجلة',
        ];
    }

    public function calculateProjectHealth()
    {
        $healthScore = 0;
        $factors = 0;

        // عامل الوقت
        if ($this->remaining_days > 0) {
            $timeProgress = (($this->duration_in_days - $this->remaining_days) / $this->duration_in_days) * 100;
            if ($this->completion_percentage >= $timeProgress) {
                $healthScore += 25;
            } elseif ($this->completion_percentage >= $timeProgress * 0.8) {
                $healthScore += 15;
            } else {
                $healthScore += 5;
            }
        }
        $factors++;

        // عامل الميزانية
        if ($this->budget_utilization_percentage <= 100) {
            $healthScore += 25;
        } elseif ($this->budget_utilization_percentage <= 110) {
            $healthScore += 15;
        } else {
            $healthScore += 5;
        }
        $factors++;

        // عامل المستفيدين
        if ($this->beneficiaries_reach_percentage >= 100) {
            $healthScore += 25;
        } elseif ($this->beneficiaries_reach_percentage >= 80) {
            $healthScore += 20;
        } elseif ($this->beneficiaries_reach_percentage >= 60) {
            $healthScore += 15;
        } else {
            $healthScore += 10;
        }
        $factors++;

        // عامل الأداء العام
        if ($this->performance_score) {
            $healthScore += ($this->performance_score / 5) * 25;
            $factors++;
        }

        return $factors > 0 ? round($healthScore / $factors, 2) : 0;
    }
}
