<?php
session_start();

// معالجة تسجيل الدخول السريع
if (isset($_GET['user'])) {
    try {
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=sector_management_system;charset=utf8mb4', 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $userEmails = [
            'admin' => '<EMAIL>',
            'org_admin' => '<EMAIL>',
            'hr' => '<EMAIL>',
            'projects' => '<EMAIL>',
            'finance' => '<EMAIL>'
        ];
        
        $email = $userEmails[$_GET['user']] ?? null;
        
        if ($email) {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['organization_id'] = $user['organization_id'];
                
                // تحديث وقت آخر دخول
                $stmt = $pdo->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                header('Location: dashboard.php');
                exit;
            }
        }
    } catch (PDOException $e) {
        $error = "خطأ في الاتصال: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول سريع - نظام إدارة مؤسسات القطاع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            max-width: 600px;
            width: 100%;
        }
        
        .user-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }
        
        .user-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
            color: inherit;
            text-decoration: none;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            font-size: 2rem;
            color: white;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="text-center mb-4">
            <div class="logo">
                <i class="fas fa-building"></i>
            </div>
            <h2 class="fw-bold text-dark">دخول سريع للنظام</h2>
            <p class="text-muted">اختر المستخدم للدخول مباشرة</p>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <a href="?user=admin" class="user-card d-block">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">مدير النظام</h5>
                            <p class="mb-0 text-muted"><EMAIL></p>
                            <small class="text-muted">صلاحيات كاملة على النظام</small>
                        </div>
                        <div>
                            <i class="fas fa-arrow-left text-muted"></i>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-12">
                <a href="?user=org_admin" class="user-card d-block">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-building"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">مدير المنظمة</h5>
                            <p class="mb-0 text-muted"><EMAIL></p>
                            <small class="text-muted">إدارة جمعية الخير الخيرية</small>
                        </div>
                        <div>
                            <i class="fas fa-arrow-left text-muted"></i>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-12">
                <a href="?user=hr" class="user-card d-block">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">مدير الموارد البشرية</h5>
                            <p class="mb-0 text-muted"><EMAIL></p>
                            <small class="text-muted">إدارة الموظفين والمتطوعين</small>
                        </div>
                        <div>
                            <i class="fas fa-arrow-left text-muted"></i>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-12">
                <a href="?user=projects" class="user-card d-block">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-project-diagram"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">مدير المشاريع</h5>
                            <p class="mb-0 text-muted"><EMAIL></p>
                            <small class="text-muted">إدارة المشاريع والبرامج</small>
                        </div>
                        <div>
                            <i class="fas fa-arrow-left text-muted"></i>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-12">
                <a href="?user=finance" class="user-card d-block">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">مدير المالية</h5>
                            <p class="mb-0 text-muted"><EMAIL></p>
                            <small class="text-muted">إدارة الشؤون المالية والتبرعات</small>
                        </div>
                        <div>
                            <i class="fas fa-arrow-left text-muted"></i>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="text-center mt-4">
            <hr>
            <p class="text-muted mb-2">أو</p>
            <a href="simple_login.php" class="btn btn-outline-primary">
                <i class="fas fa-sign-in-alt"></i> تسجيل دخول عادي
            </a>
            <a href="test.php" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-vial"></i> صفحة الاختبار
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
